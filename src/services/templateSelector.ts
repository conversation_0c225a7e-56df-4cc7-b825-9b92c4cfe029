import * as vscode from 'vscode'
import { TemplateEngine, Template, TemplateContext, TemplateMatch } from './templateEngine'
import { Logger } from '../utils/logger'
import { StorageService } from './storageService'

export interface SelectionCriteria {
  fileType?: string;
  projectType?: string;
  category?: string;
  priority?: number;
  userPreferences?: any;
  context?: any;
}

export interface SelectionResult {
  selectedTemplate: Template;
  confidence: number;
  alternatives: TemplateMatch[];
  reasons: string[];
}

export class TemplateSelector {
  private userPreferences: any = {}
  private selectionHistory: Map<string, number> = new Map()
  private templateUsageStats: Map<string, number> = new Map()

  constructor (
    private readonly logger: Logger,
    private readonly storageService: StorageService,
    private readonly templateEngine: TemplateEngine
  ) {}

  async initialize (): Promise<void> {
    try {
      this.logger.info('Initializing Template Selector...')

      // Load user preferences
      this.userPreferences = await this.storageService.getUserPreferences()

      // Load selection history
      const history = await this.storageService.getGlobalValue<[string, number][]>('templateSelectionHistory', [])
      this.selectionHistory = new Map(history)

      // Load usage statistics
      const stats = await this.storageService.getGlobalValue<[string, number][]>('templateUsageStats', [])
      this.templateUsageStats = new Map(stats)

      this.logger.info('Template Selector initialized successfully')
    } catch (error) {
      this.logger.error('Failed to initialize Template Selector:', error)
      throw error
    }
  }

  async selectTemplate (
    input: string,
    context: TemplateContext,
    criteria?: SelectionCriteria
  ): Promise<SelectionResult> {
    try {
      this.logger.debug('Selecting template for input:', input)

      // Enhance context with criteria
      const enhancedContext = this.enhanceContext(context, criteria)

      // Get all potential matches
      const matches = this.findAllMatches(enhancedContext)

      // Apply user preferences
      const preferenceAdjustedMatches = this.applyUserPreferences(matches)

      // Apply usage history
      const historyAdjustedMatches = this.applyUsageHistory(preferenceAdjustedMatches)

      // Sort by final score
      const sortedMatches = historyAdjustedMatches.sort((a, b) => b.score - a.score)

      if (sortedMatches.length === 0) {
        throw new Error('No suitable template found')
      }

      const selectedMatch = sortedMatches[0]
      if (!selectedMatch) {
        throw new Error('No suitable template found')
      }

      const alternatives = sortedMatches.slice(1, 4) // Top 3 alternatives

      // Record selection
      await this.recordSelection(selectedMatch.template.id, enhancedContext)

      const result: SelectionResult = {
        selectedTemplate: selectedMatch.template,
        confidence: this.calculateConfidence(selectedMatch.score, sortedMatches),
        alternatives,
        reasons: selectedMatch.reasons
      }

      this.logger.debug(`Selected template: ${selectedMatch.template.id} (confidence: ${result.confidence}%)`)
      return result

    } catch (error) {
      this.logger.error('Failed to select template:', error)
      throw error
    }
  }

  private enhanceContext (context: TemplateContext, criteria?: SelectionCriteria): TemplateContext {
    const enhanced: TemplateContext = { ...context }

    if (criteria) {
      if (criteria.fileType) {
        enhanced.fileType = criteria.fileType
      }
      if (criteria.projectType) {
        enhanced.projectType = criteria.projectType
      }
      enhanced.userPreferences = { ...enhanced.userPreferences, ...criteria.userPreferences }
    }

    // Add current editor context if available
    const editor = vscode.window.activeTextEditor
    if (editor && !enhanced.activeFile) {
      enhanced.activeFile = editor.document.fileName
      enhanced.fileType = enhanced.fileType || this.getFileType(editor.document.fileName)
      enhanced.cursorPosition = editor.selection.active

      if (editor.selection && !editor.selection.isEmpty) {
        enhanced.selectedText = editor.document.getText(editor.selection)
      }
    }

    // Add workspace context
    if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
      const workspaceFolder = vscode.workspace.workspaceFolders[0]
      if (workspaceFolder) {
        enhanced.workspacePath = workspaceFolder.uri.fsPath
        enhanced.projectType = enhanced.projectType || this.detectProjectType(enhanced.workspacePath)
      }
    }

    // Add timestamp
    enhanced.timestamp = new Date()

    return enhanced
  }

  private findAllMatches (context: TemplateContext): TemplateMatch[] {
    const allTemplates = this.templateEngine.getAllTemplates()
    const matches: TemplateMatch[] = []

    for (const template of allTemplates) {
      const score = this.calculateTemplateScore(template, context)
      if (score > 0) {
        matches.push({
          template,
          score,
          reasons: this.getMatchReasons(template, context)
        })
      }
    }

    return matches
  }

  private calculateTemplateScore (template: Template, context: TemplateContext): number {
    let score = 0

    // Base priority
    score += template.priority * 2

    // File type match
    if (context.fileType && template.fileTypes.includes(context.fileType)) {
      score += 15
    }

    // Project type match
    if (context.projectType && template.projectTypes.includes(context.projectType)) {
      score += 20
    }

    // Category relevance
    if (context.input) {
      const inputLower = context.input.toLowerCase()
      const categoryLower = template.category.toLowerCase()

      if (inputLower.includes(categoryLower)) {
        score += 10
      }

      // Check for specific keywords
      const keywords = this.extractKeywords(inputLower)
      const templateKeywords = this.extractTemplateKeywords(template)

      const keywordMatches = keywords.filter(keyword =>
        templateKeywords.some(templateKeyword => templateKeyword.includes(keyword))
      )

      score += keywordMatches.length * 3
    }

    // Context features
    if (context.selectedText && template.content.includes('selectedText')) {
      score += 12
    }

    if (context.codeContext && template.content.includes('codeContext')) {
      score += 8
    }

    if (context.fileContext && template.content.includes('fileContext')) {
      score += 6
    }

    // Template quality factors
    if (template.version && template.version !== '1.0.0') {
      score += 2 // Newer/updated templates
    }

    return score
  }

  private applyUserPreferences (matches: TemplateMatch[]): TemplateMatch[] {
    return matches.map(match => {
      let adjustedScore = match.score

      // Apply user template preferences
      if (this.userPreferences && this.userPreferences.preferredTemplates) {
        const preferredTemplates = this.userPreferences.preferredTemplates as string[]
        if (preferredTemplates.includes(match.template.id)) {
          adjustedScore += 10
          match.reasons.push('User preferred template')
        }
      }

      // Apply category preferences
      if (this.userPreferences && this.userPreferences.preferredCategories) {
        const preferredCategories = this.userPreferences.preferredCategories as string[]
        if (preferredCategories.includes(match.template.category)) {
          adjustedScore += 5
          match.reasons.push('User preferred category')
        }
      }

      // Apply complexity preferences
      if (this.userPreferences && this.userPreferences.complexityPreference) {
        const complexityPref = this.userPreferences.complexityPreference as string
        if (
          (complexityPref === 'simple' && match.template.priority <= 3) ||
                    (complexityPref === 'detailed' && match.template.priority >= 7)
        ) {
          adjustedScore += 3
          match.reasons.push('Matches complexity preference')
        }
      }

      return {
        ...match,
        score: adjustedScore
      }
    })
  }

  private applyUsageHistory (matches: TemplateMatch[]): TemplateMatch[] {
    return matches.map(match => {
      let adjustedScore = match.score

      // Apply usage frequency
      const usageCount = this.templateUsageStats.get(match.template.id) || 0
      if (usageCount > 0) {
        adjustedScore += Math.min(usageCount * 0.5, 5) // Max 5 points from usage
        match.reasons.push(`Used ${usageCount} times previously`)
      }

      // Apply recent selection history
      const recentSelections = this.selectionHistory.get(match.template.id) || 0
      if (recentSelections > 0) {
        adjustedScore += Math.min(recentSelections * 0.3, 3) // Max 3 points from recent use
        match.reasons.push('Recently selected')
      }

      return {
        ...match,
        score: adjustedScore
      }
    })
  }

  private calculateConfidence (selectedScore: number, allMatches: TemplateMatch[]): number {
    if (allMatches.length === 0) {return 0}
    if (allMatches.length === 1) {return 100}

    const secondBestScore = allMatches.length > 1 ? (allMatches[1]?.score || 0) : 0
    const maxPossibleScore = 100 // Theoretical maximum

    // Confidence based on score difference and absolute score
    const scoreDifference = selectedScore - secondBestScore
    const absoluteConfidence = (selectedScore / maxPossibleScore) * 100
    const relativeConfidence = Math.min((scoreDifference / selectedScore) * 100, 100)

    // Weighted average
    const confidence = Math.round((absoluteConfidence * 0.6) + (relativeConfidence * 0.4))

    return Math.max(0, Math.min(100, confidence))
  }

  private async recordSelection (templateId: string, _context: TemplateContext): Promise<void> {
    try {
      // Update usage statistics
      const currentUsage = this.templateUsageStats.get(templateId) || 0
      this.templateUsageStats.set(templateId, currentUsage + 1)

      // Update selection history (with decay for older selections)
      const currentHistory = this.selectionHistory.get(templateId) || 0
      this.selectionHistory.set(templateId, currentHistory + 1)

      // Decay older selections
      for (const [id, count] of this.selectionHistory.entries()) {
        if (id !== templateId) {
          this.selectionHistory.set(id, Math.max(0, count * 0.95))
        }
      }

      // Save to storage
      await this.storageService.setGlobalValue(
        'templateUsageStats',
        Array.from(this.templateUsageStats.entries())
      )

      await this.storageService.setGlobalValue(
        'templateSelectionHistory',
        Array.from(this.selectionHistory.entries())
      )

      this.logger.debug(`Recorded selection for template: ${templateId}`)
    } catch (error) {
      this.logger.warn('Failed to record template selection:', error)
    }
  }

  private getMatchReasons (template: Template, context: TemplateContext): string[] {
    const reasons: string[] = []

    if (context.fileType && template.fileTypes.includes(context.fileType)) {
      reasons.push(`Matches file type: ${context.fileType}`)
    }

    if (context.projectType && template.projectTypes.includes(context.projectType)) {
      reasons.push(`Matches project type: ${context.projectType}`)
    }

    if (context.input) {
      const inputLower = context.input.toLowerCase()
      const categoryLower = template.category.toLowerCase()

      if (inputLower.includes(categoryLower)) {
        reasons.push(`Matches category: ${template.category}`)
      }
    }

    if (template.priority > 7) {
      reasons.push('High priority template')
    }

    if (context.selectedText && template.content.includes('selectedText')) {
      reasons.push('Supports selected text context')
    }

    return reasons
  }

  private extractKeywords (input: string): string[] {
    const keywords = input
      .toLowerCase()
      .split(/\W+/)
      .filter(word => word.length > 3)
      .filter(word => !this.isStopWord(word))

    return [...new Set(keywords)]
  }

  private extractTemplateKeywords (template: Template): string[] {
    const text = `${template.name} ${template.description} ${template.category}`.toLowerCase()
    return this.extractKeywords(text)
  }

  private isStopWord (word: string): boolean {
    const stopWords = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'use', 'man', 'new', 'now', 'way', 'may', 'say']
    return stopWords.includes(word)
  }

  private getFileType (fileName: string): string {
    const ext = fileName.split('.').pop()?.toLowerCase()
    return ext || 'unknown'
  }

  private detectProjectType (workspacePath: string): string {
    // Simple project type detection logic
    try {
      const fs = require('fs')
      const path = require('path')

      const packageJsonPath = path.join(workspacePath, 'package.json')
      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'))

        if (packageJson && packageJson.dependencies) {
          if (packageJson.dependencies.react) {return 'react'}
          if (packageJson.dependencies.vue) {return 'vue'}
          if (packageJson.dependencies.angular) {return 'angular'}
          if (packageJson.dependencies.express) {return 'express'}
          if (packageJson.dependencies.next) {return 'next'}
        }

        return 'node'
      }

      const requirementsPath = path.join(workspacePath, 'requirements.txt')
      if (fs.existsSync(requirementsPath)) {
        return 'python'
      }

      const cargoPath = path.join(workspacePath, 'Cargo.toml')
      if (fs.existsSync(cargoPath)) {
        return 'rust'
      }

      const goModPath = path.join(workspacePath, 'go.mod')
      if (fs.existsSync(goModPath)) {
        return 'go'
      }

      return 'unknown'
    } catch (error) {
      return 'unknown'
    }
  }

  async updateUserPreferences (preferences: any): Promise<void> {
    this.userPreferences = { ...this.userPreferences, ...preferences }
    await this.storageService.setUserPreferences(this.userPreferences)
    this.logger.info('User preferences updated')
  }

  async getSelectionStatistics (): Promise<any> {
    return {
      totalSelections: Array.from(this.templateUsageStats.values()).reduce((sum, count) => sum + count, 0),
      templateUsage: Object.fromEntries(this.templateUsageStats),
      recentSelections: Object.fromEntries(this.selectionHistory),
      userPreferences: this.userPreferences
    }
  }

  async resetStatistics (): Promise<void> {
    this.templateUsageStats.clear()
    this.selectionHistory.clear()

    await this.storageService.setGlobalValue('templateUsageStats', [])
    await this.storageService.setGlobalValue('templateSelectionHistory', [])

    this.logger.info('Template selection statistics reset')
  }
}
