"use strict";
// Copyright 2023 LanceDB Developers.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.sanitizeSchema = exports.sanitizeField = exports.sanitizeType = exports.sanitizeDictionary = exports.sanitizeDuration = exports.sanitizeMap = exports.sanitizeFixedSizeList = exports.sanitizeFixedSizeBinary = exports.sanitizeTypedUnion = exports.sanitizeUnion = exports.sanitizeStruct = exports.sanitizeList = exports.sanitizeInterval = exports.sanitizeTypedTimestamp = exports.sanitizeTimestamp = exports.sanitizeTime = exports.sanitizeDate = exports.sanitizeDecimal = exports.sanitizeFloat = exports.sanitizeInt = exports.sanitizeMetadata = void 0;
const arrow_1 = require("./arrow");
function sanitizeMetadata(metadataLike) {
    if (metadataLike === undefined || metadataLike === null) {
        return undefined;
    }
    if (!(metadataLike instanceof Map)) {
        throw Error("Expected metadata, if present, to be a Map<string, string>");
    }
    for (const item of metadataLike) {
        if (!(typeof item[0] === "string" || !(typeof item[1] === "string"))) {
            throw Error("Expected metadata, if present, to be a Map<string, string> but it had non-string keys or values");
        }
    }
    return metadataLike;
}
exports.sanitizeMetadata = sanitizeMetadata;
function sanitizeInt(typeLike) {
    if (!("bitWidth" in typeLike) ||
        typeof typeLike.bitWidth !== "number" ||
        !("isSigned" in typeLike) ||
        typeof typeLike.isSigned !== "boolean") {
        throw Error("Expected an Int Type to have a `bitWidth` and `isSigned` property");
    }
    return new arrow_1.Int(typeLike.isSigned, typeLike.bitWidth);
}
exports.sanitizeInt = sanitizeInt;
function sanitizeFloat(typeLike) {
    if (!("precision" in typeLike) || typeof typeLike.precision !== "number") {
        throw Error("Expected a Float Type to have a `precision` property");
    }
    return new arrow_1.Float(typeLike.precision);
}
exports.sanitizeFloat = sanitizeFloat;
function sanitizeDecimal(typeLike) {
    if (!("scale" in typeLike) ||
        typeof typeLike.scale !== "number" ||
        !("precision" in typeLike) ||
        typeof typeLike.precision !== "number" ||
        !("bitWidth" in typeLike) ||
        typeof typeLike.bitWidth !== "number") {
        throw Error("Expected a Decimal Type to have `scale`, `precision`, and `bitWidth` properties");
    }
    return new arrow_1.Decimal(typeLike.scale, typeLike.precision, typeLike.bitWidth);
}
exports.sanitizeDecimal = sanitizeDecimal;
function sanitizeDate(typeLike) {
    if (!("unit" in typeLike) || typeof typeLike.unit !== "number") {
        throw Error("Expected a Date type to have a `unit` property");
    }
    return new arrow_1.Date_(typeLike.unit);
}
exports.sanitizeDate = sanitizeDate;
function sanitizeTime(typeLike) {
    if (!("unit" in typeLike) ||
        typeof typeLike.unit !== "number" ||
        !("bitWidth" in typeLike) ||
        typeof typeLike.bitWidth !== "number") {
        throw Error("Expected a Time type to have `unit` and `bitWidth` properties");
    }
    return new arrow_1.Time(typeLike.unit, typeLike.bitWidth);
}
exports.sanitizeTime = sanitizeTime;
function sanitizeTimestamp(typeLike) {
    if (!("unit" in typeLike) || typeof typeLike.unit !== "number") {
        throw Error("Expected a Timestamp type to have a `unit` property");
    }
    let timezone = null;
    if ("timezone" in typeLike && typeof typeLike.timezone === "string") {
        timezone = typeLike.timezone;
    }
    return new arrow_1.Timestamp(typeLike.unit, timezone);
}
exports.sanitizeTimestamp = sanitizeTimestamp;
function sanitizeTypedTimestamp(typeLike, 
// eslint-disable-next-line @typescript-eslint/naming-convention
Datatype) {
    let timezone = null;
    if ("timezone" in typeLike && typeof typeLike.timezone === "string") {
        timezone = typeLike.timezone;
    }
    return new Datatype(timezone);
}
exports.sanitizeTypedTimestamp = sanitizeTypedTimestamp;
function sanitizeInterval(typeLike) {
    if (!("unit" in typeLike) || typeof typeLike.unit !== "number") {
        throw Error("Expected an Interval type to have a `unit` property");
    }
    return new arrow_1.Interval(typeLike.unit);
}
exports.sanitizeInterval = sanitizeInterval;
function sanitizeList(typeLike) {
    if (!("children" in typeLike) || !Array.isArray(typeLike.children)) {
        throw Error("Expected a List type to have an array-like `children` property");
    }
    if (typeLike.children.length !== 1) {
        throw Error("Expected a List type to have exactly one child");
    }
    return new arrow_1.List(sanitizeField(typeLike.children[0]));
}
exports.sanitizeList = sanitizeList;
function sanitizeStruct(typeLike) {
    if (!("children" in typeLike) || !Array.isArray(typeLike.children)) {
        throw Error("Expected a Struct type to have an array-like `children` property");
    }
    return new arrow_1.Struct(typeLike.children.map((child) => sanitizeField(child)));
}
exports.sanitizeStruct = sanitizeStruct;
function sanitizeUnion(typeLike) {
    if (!("typeIds" in typeLike) ||
        !("mode" in typeLike) ||
        typeof typeLike.mode !== "number") {
        throw Error("Expected a Union type to have `typeIds` and `mode` properties");
    }
    if (!("children" in typeLike) || !Array.isArray(typeLike.children)) {
        throw Error("Expected a Union type to have an array-like `children` property");
    }
    return new arrow_1.Union(typeLike.mode, 
    // biome-ignore lint/suspicious/noExplicitAny: skip
    typeLike.typeIds, typeLike.children.map((child) => sanitizeField(child)));
}
exports.sanitizeUnion = sanitizeUnion;
function sanitizeTypedUnion(typeLike, 
// eslint-disable-next-line @typescript-eslint/naming-convention
UnionType) {
    if (!("typeIds" in typeLike)) {
        throw Error("Expected a DenseUnion/SparseUnion type to have a `typeIds` property");
    }
    if (!("children" in typeLike) || !Array.isArray(typeLike.children)) {
        throw Error("Expected a DenseUnion/SparseUnion type to have an array-like `children` property");
    }
    return new UnionType(typeLike.typeIds, typeLike.children.map((child) => sanitizeField(child)));
}
exports.sanitizeTypedUnion = sanitizeTypedUnion;
function sanitizeFixedSizeBinary(typeLike) {
    if (!("byteWidth" in typeLike) || typeof typeLike.byteWidth !== "number") {
        throw Error("Expected a FixedSizeBinary type to have a `byteWidth` property");
    }
    return new arrow_1.FixedSizeBinary(typeLike.byteWidth);
}
exports.sanitizeFixedSizeBinary = sanitizeFixedSizeBinary;
function sanitizeFixedSizeList(typeLike) {
    if (!("listSize" in typeLike) || typeof typeLike.listSize !== "number") {
        throw Error("Expected a FixedSizeList type to have a `listSize` property");
    }
    if (!("children" in typeLike) || !Array.isArray(typeLike.children)) {
        throw Error("Expected a FixedSizeList type to have an array-like `children` property");
    }
    if (typeLike.children.length !== 1) {
        throw Error("Expected a FixedSizeList type to have exactly one child");
    }
    return new arrow_1.FixedSizeList(typeLike.listSize, sanitizeField(typeLike.children[0]));
}
exports.sanitizeFixedSizeList = sanitizeFixedSizeList;
function sanitizeMap(typeLike) {
    if (!("children" in typeLike) || !Array.isArray(typeLike.children)) {
        throw Error("Expected a Map type to have an array-like `children` property");
    }
    if (!("keysSorted" in typeLike) || typeof typeLike.keysSorted !== "boolean") {
        throw Error("Expected a Map type to have a `keysSorted` property");
    }
    return new arrow_1.Map_(
    // biome-ignore lint/suspicious/noExplicitAny: skip
    typeLike.children.map((field) => sanitizeField(field)), typeLike.keysSorted);
}
exports.sanitizeMap = sanitizeMap;
function sanitizeDuration(typeLike) {
    if (!("unit" in typeLike) || typeof typeLike.unit !== "number") {
        throw Error("Expected a Duration type to have a `unit` property");
    }
    return new arrow_1.Duration(typeLike.unit);
}
exports.sanitizeDuration = sanitizeDuration;
function sanitizeDictionary(typeLike) {
    if (!("id" in typeLike) || typeof typeLike.id !== "number") {
        throw Error("Expected a Dictionary type to have an `id` property");
    }
    if (!("indices" in typeLike) || typeof typeLike.indices !== "object") {
        throw Error("Expected a Dictionary type to have an `indices` property");
    }
    if (!("dictionary" in typeLike) || typeof typeLike.dictionary !== "object") {
        throw Error("Expected a Dictionary type to have an `dictionary` property");
    }
    if (!("isOrdered" in typeLike) || typeof typeLike.isOrdered !== "boolean") {
        throw Error("Expected a Dictionary type to have an `isOrdered` property");
    }
    return new arrow_1.Dictionary(sanitizeType(typeLike.dictionary), sanitizeType(typeLike.indices), typeLike.id, typeLike.isOrdered);
}
exports.sanitizeDictionary = sanitizeDictionary;
// biome-ignore lint/suspicious/noExplicitAny: skip
function sanitizeType(typeLike) {
    if (typeof typeLike !== "object" || typeLike === null) {
        throw Error("Expected a Type but object was null/undefined");
    }
    if (!("typeId" in typeLike) || !(typeof typeLike.typeId !== "function")) {
        throw Error("Expected a Type to have a typeId function");
    }
    let typeId;
    if (typeof typeLike.typeId === "function") {
        typeId = typeLike.typeId();
    }
    else if (typeof typeLike.typeId === "number") {
        typeId = typeLike.typeId;
    }
    else {
        throw Error("Type's typeId property was not a function or number");
    }
    switch (typeId) {
        case arrow_1.Type.NONE:
            throw Error("Received a Type with a typeId of NONE");
        case arrow_1.Type.Null:
            return new arrow_1.Null();
        case arrow_1.Type.Int:
            return sanitizeInt(typeLike);
        case arrow_1.Type.Float:
            return sanitizeFloat(typeLike);
        case arrow_1.Type.Binary:
            return new arrow_1.Binary();
        case arrow_1.Type.Utf8:
            return new arrow_1.Utf8();
        case arrow_1.Type.Bool:
            return new arrow_1.Bool();
        case arrow_1.Type.Decimal:
            return sanitizeDecimal(typeLike);
        case arrow_1.Type.Date:
            return sanitizeDate(typeLike);
        case arrow_1.Type.Time:
            return sanitizeTime(typeLike);
        case arrow_1.Type.Timestamp:
            return sanitizeTimestamp(typeLike);
        case arrow_1.Type.Interval:
            return sanitizeInterval(typeLike);
        case arrow_1.Type.List:
            return sanitizeList(typeLike);
        case arrow_1.Type.Struct:
            return sanitizeStruct(typeLike);
        case arrow_1.Type.Union:
            return sanitizeUnion(typeLike);
        case arrow_1.Type.FixedSizeBinary:
            return sanitizeFixedSizeBinary(typeLike);
        case arrow_1.Type.FixedSizeList:
            return sanitizeFixedSizeList(typeLike);
        case arrow_1.Type.Map:
            return sanitizeMap(typeLike);
        case arrow_1.Type.Duration:
            return sanitizeDuration(typeLike);
        case arrow_1.Type.Dictionary:
            return sanitizeDictionary(typeLike);
        case arrow_1.Type.Int8:
            return new arrow_1.Int8();
        case arrow_1.Type.Int16:
            return new arrow_1.Int16();
        case arrow_1.Type.Int32:
            return new arrow_1.Int32();
        case arrow_1.Type.Int64:
            return new arrow_1.Int64();
        case arrow_1.Type.Uint8:
            return new arrow_1.Uint8();
        case arrow_1.Type.Uint16:
            return new arrow_1.Uint16();
        case arrow_1.Type.Uint32:
            return new arrow_1.Uint32();
        case arrow_1.Type.Uint64:
            return new arrow_1.Uint64();
        case arrow_1.Type.Float16:
            return new arrow_1.Float16();
        case arrow_1.Type.Float32:
            return new arrow_1.Float32();
        case arrow_1.Type.Float64:
            return new arrow_1.Float64();
        case arrow_1.Type.DateMillisecond:
            return new arrow_1.DateMillisecond();
        case arrow_1.Type.DateDay:
            return new arrow_1.DateDay();
        case arrow_1.Type.TimeNanosecond:
            return new arrow_1.TimeNanosecond();
        case arrow_1.Type.TimeMicrosecond:
            return new arrow_1.TimeMicrosecond();
        case arrow_1.Type.TimeMillisecond:
            return new arrow_1.TimeMillisecond();
        case arrow_1.Type.TimeSecond:
            return new arrow_1.TimeSecond();
        case arrow_1.Type.TimestampNanosecond:
            return sanitizeTypedTimestamp(typeLike, arrow_1.TimestampNanosecond);
        case arrow_1.Type.TimestampMicrosecond:
            return sanitizeTypedTimestamp(typeLike, arrow_1.TimestampMicrosecond);
        case arrow_1.Type.TimestampMillisecond:
            return sanitizeTypedTimestamp(typeLike, arrow_1.TimestampMillisecond);
        case arrow_1.Type.TimestampSecond:
            return sanitizeTypedTimestamp(typeLike, arrow_1.TimestampSecond);
        case arrow_1.Type.DenseUnion:
            return sanitizeTypedUnion(typeLike, arrow_1.DenseUnion);
        case arrow_1.Type.SparseUnion:
            return sanitizeTypedUnion(typeLike, arrow_1.SparseUnion);
        case arrow_1.Type.IntervalDayTime:
            return new arrow_1.IntervalDayTime();
        case arrow_1.Type.IntervalYearMonth:
            return new arrow_1.IntervalYearMonth();
        case arrow_1.Type.DurationNanosecond:
            return new arrow_1.DurationNanosecond();
        case arrow_1.Type.DurationMicrosecond:
            return new arrow_1.DurationMicrosecond();
        case arrow_1.Type.DurationMillisecond:
            return new arrow_1.DurationMillisecond();
        case arrow_1.Type.DurationSecond:
            return new arrow_1.DurationSecond();
        default:
            throw new Error("Unrecoginized type id in schema: " + typeId);
    }
}
exports.sanitizeType = sanitizeType;
function sanitizeField(fieldLike) {
    if (fieldLike instanceof arrow_1.Field) {
        return fieldLike;
    }
    if (typeof fieldLike !== "object" || fieldLike === null) {
        throw Error("Expected a Field but object was null/undefined");
    }
    if (!("type" in fieldLike) ||
        !("name" in fieldLike) ||
        !("nullable" in fieldLike)) {
        throw Error("The field passed in is missing a `type`/`name`/`nullable` property");
    }
    const type = sanitizeType(fieldLike.type);
    const name = fieldLike.name;
    if (!(typeof name === "string")) {
        throw Error("The field passed in had a non-string `name` property");
    }
    const nullable = fieldLike.nullable;
    if (!(typeof nullable === "boolean")) {
        throw Error("The field passed in had a non-boolean `nullable` property");
    }
    let metadata;
    if ("metadata" in fieldLike) {
        metadata = sanitizeMetadata(fieldLike.metadata);
    }
    return new arrow_1.Field(name, type, nullable, metadata);
}
exports.sanitizeField = sanitizeField;
/**
 * Convert something schemaLike into a Schema instance
 *
 * This method is often needed even when the caller is using a Schema
 * instance because they might be using a different instance of apache-arrow
 * than lancedb is using.
 */
function sanitizeSchema(schemaLike) {
    if (schemaLike instanceof arrow_1.Schema) {
        return schemaLike;
    }
    if (typeof schemaLike !== "object" || schemaLike === null) {
        throw Error("Expected a Schema but object was null/undefined");
    }
    if (!("fields" in schemaLike)) {
        throw Error("The schema passed in does not appear to be a schema (no 'fields' property)");
    }
    let metadata;
    if ("metadata" in schemaLike) {
        metadata = sanitizeMetadata(schemaLike.metadata);
    }
    if (!Array.isArray(schemaLike.fields)) {
        throw Error("The schema passed in had a 'fields' property but it was not an array");
    }
    const sanitizedFields = schemaLike.fields.map((field) => sanitizeField(field));
    return new arrow_1.Schema(sanitizedFields, metadata);
}
exports.sanitizeSchema = sanitizeSchema;
