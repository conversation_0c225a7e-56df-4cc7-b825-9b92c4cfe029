import { Builder } from '../builder.js';
import { Struct, TypeMap } from '../type.js';
/** @ignore */
export declare class StructBuilder<T extends TypeMap = any, TNull = any> extends Builder<Struct<T>, TNull> {
    setValue(index: number, value: Struct<T>['TValue']): void;
    /** @inheritdoc */
    setValid(index: number, valid: boolean): boolean;
    addChild(child: Builder, name?: string): number;
}
