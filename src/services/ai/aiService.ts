import * as vscode from 'vscode'
import { AIProvider, AIContext, AIResponse, AIConfig, AIProviderType, AIMetrics } from '../../types/aiTypes'
import { OpenAIProvider } from './openaiProvider'
import { ClaudeProvider } from './claudeProvider'
import { TokenManager } from './tokenManager'
import { AIErrorHandler } from './errorHandler'
import { Logger } from '../../utils/logger'
import { StorageService } from '../storageService'

export class AIService {
  private readonly providers: Map<AIProviderType, AIProvider> = new Map()
  private currentProvider: AIProvider | null = null
  private readonly tokenManager: TokenManager
  private readonly errorHandler: AIErrorHandler
  private metrics: AIMetrics

  constructor (
    private readonly logger: Logger,
    private readonly storageService: StorageService
  ) {
    this.tokenManager = new TokenManager()
    this.errorHandler = new AIErrorHandler(logger)
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      totalTokensUsed: 0,
      averageTokensPerRequest: 0
    }
  }

  async initialize (): Promise<void> {
    try {
      this.logger.info('Initializing AI Service...')

      // Initialize providers
      await this.initializeProviders()

      // Set current provider based on configuration
      await this.setCurrentProvider()

      this.logger.info('AI Service initialized successfully')
    } catch (error) {
      this.logger.error('Failed to initialize AI Service:', error)
      throw error
    }
  }

  private async initializeProviders (): Promise<void> {
    const config = await this.getConfig()

    // Initialize OpenAI provider
    const openaiProvider = new OpenAIProvider(this.logger, this.storageService)
    await openaiProvider.initialize(config)
    this.providers.set(AIProviderType.OPENAI, openaiProvider)

    // Initialize Claude provider
    const claudeProvider = new ClaudeProvider(this.logger, this.storageService)
    await claudeProvider.initialize(config)
    this.providers.set(AIProviderType.CLAUDE, claudeProvider)
  }

  private async setCurrentProvider (): Promise<void> {
    const config = await this.getConfig()
    const providerType = config.provider as AIProviderType

    this.currentProvider = this.providers.get(providerType) || null

    if (!this.currentProvider) {
      this.logger.warn(`Provider ${providerType} not found, falling back to OpenAI`)
      this.currentProvider = this.providers.get(AIProviderType.OPENAI) || null
    }

    if (!this.currentProvider) {
      throw new Error('No AI provider available')
    }
  }

  async generateResponse (prompt: string, context?: AIContext): Promise<AIResponse> {
    const startTime = Date.now()
    this.metrics.totalRequests++

    try {
      if (!this.currentProvider) {
        throw new Error('No AI provider configured')
      }

      // Optimize prompt and context for token limits
      const optimizedContext = await this.tokenManager.optimizeContext(prompt, context)

      // Generate response with retry logic
      const response = await this.errorHandler.executeWithRetry(
        async () => {
          if (!this.currentProvider) {
            throw new Error('No AI provider configured')
          }
          return await this.currentProvider.generateResponse(prompt, optimizedContext)
        },
        this.currentProvider.name
      )

      // Update metrics
      const responseTime = Date.now() - startTime
      this.updateMetrics(responseTime, response.tokensUsed || 0, true)

      this.logger.info(`AI response generated successfully in ${responseTime}ms`)
      return response

    } catch (error) {
      const responseTime = Date.now() - startTime
      this.updateMetrics(responseTime, 0, false)

      this.logger.error('Failed to generate AI response:', error)

      // Try fallback provider
      const fallbackResponse = await this.tryFallbackProvider(prompt, context)
      if (fallbackResponse) {
        return fallbackResponse
      }

      throw error
    }
  }

  private async tryFallbackProvider (prompt: string, context?: AIContext): Promise<AIResponse | null> {
    try {
      this.logger.info('Attempting fallback provider...')

      const fallbackProviderType = this.currentProvider?.name === 'openai'
        ? AIProviderType.CLAUDE
        : AIProviderType.OPENAI

      const fallbackProvider = this.providers.get(fallbackProviderType)
      if (!fallbackProvider) {
        return null
      }

      const isValid = await fallbackProvider.validateConfig()
      if (!isValid) {
        this.logger.warn(`Fallback provider ${fallbackProviderType} is not properly configured`)
        return null
      }

      const optimizedContext = await this.tokenManager.optimizeContext(prompt, context)
      const response = await fallbackProvider.generateResponse(prompt, optimizedContext)

      this.logger.info(`Fallback provider ${fallbackProviderType} succeeded`)
      return response

    } catch (error) {
      this.logger.error('Fallback provider also failed:', error)
      return null
    }
  }

  async validateConfiguration (): Promise<boolean> {
    try {
      if (!this.currentProvider) {
        return false
      }

      return await this.currentProvider.validateConfig()
    } catch (error) {
      this.logger.error('Failed to validate AI configuration:', error)
      return false
    }
  }

  async switchProvider (providerType: AIProviderType): Promise<void> {
    const provider = this.providers.get(providerType)
    if (!provider) {
      throw new Error(`Provider ${providerType} not available`)
    }

    const isValid = await provider.validateConfig()
    if (!isValid) {
      throw new Error(`Provider ${providerType} is not properly configured`)
    }

    this.currentProvider = provider

    // Update configuration
    const config = vscode.workspace.getConfiguration('taskTransformer')
    await config.update('aiProvider', providerType, vscode.ConfigurationTarget.Global)

    this.logger.info(`Switched to AI provider: ${providerType}`)
  }

  getMetrics (): AIMetrics {
    return { ...this.metrics }
  }

  resetMetrics (): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      totalTokensUsed: 0,
      averageTokensPerRequest: 0
    }
  }

  private updateMetrics (responseTime: number, tokensUsed: number, success: boolean): void {
    if (success) {
      this.metrics.successfulRequests++
    } else {
      this.metrics.failedRequests++
    }

    this.metrics.totalTokensUsed += tokensUsed

    // Update average response time
    this.metrics.averageResponseTime = (
      (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime) /
            this.metrics.totalRequests
    )

    // Update average tokens per request
    if (this.metrics.successfulRequests > 0) {
      this.metrics.averageTokensPerRequest = this.metrics.totalTokensUsed / this.metrics.successfulRequests
    }
  }

  private async getConfig (): Promise<AIConfig> {
    const config = vscode.workspace.getConfiguration('taskTransformer')
    const apiKey = await this.storageService.getSecret('apiKey') || ''

    return {
      provider: config.get('aiProvider', 'openai') as 'openai' | 'claude',
      model: config.get('model', 'gpt-4'),
      apiKey,
      maxTokens: config.get('maxTokens', 4000),
      temperature: config.get('temperature', 0.7),
      timeout: config.get('timeout', 30000),
      retryAttempts: config.get('retryAttempts', 3),
      retryDelay: config.get('retryDelay', 1000)
    }
  }

  getCurrentProvider (): string {
    return this.currentProvider?.name || 'none'
  }

  async dispose (): Promise<void> {
    this.logger.info('Disposing AI Service...')
    // Cleanup resources if needed
  }
}
