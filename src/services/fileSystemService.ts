import * as fs from 'fs'
import * as path from 'path'
import { promisify } from 'util'

export interface FileSystemService {
  readFile(filePath: string): Promise<string>
  writeFile(filePath: string, content: string): Promise<void>
  exists(filePath: string): Promise<boolean>
  stat(filePath: string): Promise<fs.Stats>
  readdir(dirPath: string): Promise<string[]>
  mkdir(dirPath: string, options?: { recursive: boolean }): Promise<void>
  unlink(filePath: string): Promise<void>
  rmdir(dirPath: string): Promise<void>
  isFile(filePath: string): Promise<boolean>
  isDirectory(dirPath: string): Promise<boolean>
  getFileSize(filePath: string): Promise<number>
  getModifiedTime(filePath: string): Promise<Date>
  joinPath(...paths: string[]): string
  dirname(filePath: string): string
  basename(filePath: string): string
  extname(filePath: string): string
  resolve(...paths: string[]): string
  relative(from: string, to: string): string
}

export class DefaultFileSystemService implements FileSystemService {
  private readFileAsync = promisify(fs.readFile)
  private writeFileAsync = promisify(fs.writeFile)
  private statAsync = promisify(fs.stat)
  private readdirAsync = promisify(fs.readdir)
  private mkdirAsync = promisify(fs.mkdir)
  private unlinkAsync = promisify(fs.unlink)
  private rmdirAsync = promisify(fs.rmdir)

  async readFile(filePath: string): Promise<string> {
    try {
      const buffer = await this.readFileAsync(filePath)
      return buffer.toString('utf-8')
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error}`)
    }
  }

  async writeFile(filePath: string, content: string): Promise<void> {
    try {
      await this.writeFileAsync(filePath, content, 'utf-8')
    } catch (error) {
      throw new Error(`Failed to write file ${filePath}: ${error}`)
    }
  }

  async exists(filePath: string): Promise<boolean> {
    try {
      await this.statAsync(filePath)
      return true
    } catch {
      return false
    }
  }

  async stat(filePath: string): Promise<fs.Stats> {
    try {
      return await this.statAsync(filePath)
    } catch (error) {
      throw new Error(`Failed to stat ${filePath}: ${error}`)
    }
  }

  async readdir(dirPath: string): Promise<string[]> {
    try {
      return await this.readdirAsync(dirPath)
    } catch (error) {
      throw new Error(`Failed to read directory ${dirPath}: ${error}`)
    }
  }

  async mkdir(dirPath: string, options?: { recursive: boolean }): Promise<void> {
    try {
      await this.mkdirAsync(dirPath, options)
    } catch (error) {
      throw new Error(`Failed to create directory ${dirPath}: ${error}`)
    }
  }

  async unlink(filePath: string): Promise<void> {
    try {
      await this.unlinkAsync(filePath)
    } catch (error) {
      throw new Error(`Failed to delete file ${filePath}: ${error}`)
    }
  }

  async rmdir(dirPath: string): Promise<void> {
    try {
      await this.rmdirAsync(dirPath)
    } catch (error) {
      throw new Error(`Failed to remove directory ${dirPath}: ${error}`)
    }
  }

  async isFile(filePath: string): Promise<boolean> {
    try {
      const stats = await this.statAsync(filePath)
      return stats.isFile()
    } catch {
      return false
    }
  }

  async isDirectory(dirPath: string): Promise<boolean> {
    try {
      const stats = await this.statAsync(dirPath)
      return stats.isDirectory()
    } catch {
      return false
    }
  }

  async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await this.statAsync(filePath)
      return stats.size
    } catch (error) {
      throw new Error(`Failed to get file size for ${filePath}: ${error}`)
    }
  }

  async getModifiedTime(filePath: string): Promise<Date> {
    try {
      const stats = await this.statAsync(filePath)
      return stats.mtime
    } catch (error) {
      throw new Error(`Failed to get modified time for ${filePath}: ${error}`)
    }
  }

  joinPath(...paths: string[]): string {
    return path.join(...paths)
  }

  dirname(filePath: string): string {
    return path.dirname(filePath)
  }

  basename(filePath: string): string {
    return path.basename(filePath)
  }

  extname(filePath: string): string {
    return path.extname(filePath)
  }

  resolve(...paths: string[]): string {
    return path.resolve(...paths)
  }

  relative(from: string, to: string): string {
    return path.relative(from, to)
  }
}