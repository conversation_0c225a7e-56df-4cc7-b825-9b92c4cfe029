import { Template, TemplateContext } from './templateEngine'
import { Logger } from '../utils/logger'
import { StorageService } from './storageService'

export interface TemplateCustomization {
  id: string;
  templateId: string;
  name: string;
  description: string;
  modifications: TemplateModification[];
  context: TemplateContext;
  createdAt: Date;
  lastUsed: Date;
}

export interface TemplateModification {
  type: 'add' | 'remove' | 'replace' | 'move';
  target: string;
  content?: string;
  position?: number;
  condition?: string;
}

export interface CustomizationSuggestion {
  type: 'content' | 'helper' | 'condition' | 'format';
  description: string;
  modification: TemplateModification;
  confidence: number;
}

export class TemplateCustomizer {
  private customizations: Map<string, TemplateCustomization> = new Map()
  private usagePatterns: Map<string, any> = new Map()

  constructor (
    private readonly logger: Logger,
    private readonly storageService: StorageService
  ) {}

  async initialize (): Promise<void> {
    try {
      this.logger.info('Initializing Template Customizer...')

      // Load existing customizations
      const customizations = await this.storageService.getGlobalValue<[string, TemplateCustomization][]>('templateCustomizations', [])
      this.customizations = new Map(customizations)

      // Load usage patterns
      const patterns = await this.storageService.getGlobalValue<[string, any][]>('templateUsagePatterns', [])
      this.usagePatterns = new Map(patterns)

      this.logger.info(`Template Customizer initialized with ${this.customizations.size} customizations`)
    } catch (error) {
      this.logger.error('Failed to initialize Template Customizer:', error)
      throw error
    }
  }

  async customizeTemplate (
    baseTemplate: Template,
    context: TemplateContext,
    modifications: TemplateModification[]
  ): Promise<Template> {
    try {
      // Create customization record
      const customizationId = `custom_${Date.now()}`
      const customization: TemplateCustomization = {
        id: customizationId,
        templateId: baseTemplate.id,
        name: `Custom ${baseTemplate.name}`,
        description: `Customized version of ${baseTemplate.name}`,
        modifications,
        context,
        createdAt: new Date(),
        lastUsed: new Date()
      }

      // Apply modifications to template
      const customizedTemplate = await this.applyModifications(baseTemplate, modifications)

      // Store customization
      this.customizations.set(customizationId, customization)
      await this.saveCustomizations()

      this.logger.info(`Created template customization: ${customizationId}`)
      return customizedTemplate
    } catch (error) {
      this.logger.error('Failed to customize template:', error)
      throw error
    }
  }

  private async applyModifications (template: Template, modifications: TemplateModification[]): Promise<Template> {
    let customizedContent = template.content

    for (const modification of modifications) {
      switch (modification.type) {
        case 'add':
          customizedContent = this.addContent(customizedContent, modification)
          break
        case 'remove':
          customizedContent = this.removeContent(customizedContent, modification)
          break
        case 'replace':
          customizedContent = this.replaceContent(customizedContent, modification)
          break
        case 'move':
          customizedContent = this.moveContent(customizedContent, modification)
          break
      }
    }

    // Create new template with customized content
    const customizedTemplate: Template = {
      ...template,
      id: `custom_${Date.now()}`,
      name: `Custom ${template.name}`,
      content: customizedContent,
      version: `${template.version}-custom`,
      author: 'User Customized',
      lastModified: new Date()
    }

    return customizedTemplate
  }

  private addContent (content: string, modification: TemplateModification): string {
    if (!modification.content || !modification.target) {
      return content
    }

    const targetIndex = content.indexOf(modification.target)
    if (targetIndex === -1) {
      // If target not found, add at the end
      return content + '\n' + modification.content
    }

    const position = modification.position || 0 // 0 = after, 1 = before
    if (position === 1) {
      // Add before target
      return content.slice(0, targetIndex) + modification.content + '\n' + content.slice(targetIndex)
    } else {
      // Add after target
      const targetEndIndex = targetIndex + modification.target.length
      return content.slice(0, targetEndIndex) + '\n' + modification.content + content.slice(targetEndIndex)
    }
  }

  private removeContent (content: string, modification: TemplateModification): string {
    if (!modification.target) {
      return content
    }

    return content.replace(new RegExp(modification.target, 'g'), '')
  }

  private replaceContent (content: string, modification: TemplateModification): string {
    if (!modification.target || !modification.content) {
      return content
    }

    return content.replace(new RegExp(modification.target, 'g'), modification.content)
  }

  private moveContent (content: string, modification: TemplateModification): string {
    if (!modification.target) {
      return content
    }

    // Simple move implementation - remove from current position and add at new position
    const targetContent = this.extractContent(content, modification.target)
    if (!targetContent) {
      return content
    }

    const withoutTarget = this.removeContent(content, modification)
    const position = modification.position || 0

    const lines = withoutTarget.split('\n')
    const insertIndex = Math.min(position, lines.length)

    lines.splice(insertIndex, 0, targetContent)
    return lines.join('\n')
  }

  private extractContent (content: string, target: string): string | null {
    const match = content.match(new RegExp(target))
    return match ? match[0] : null
  }

  async suggestCustomizations (
    template: Template,
    context: TemplateContext
  ): Promise<CustomizationSuggestion[]> {
    const suggestions: CustomizationSuggestion[] = []

    // Analyze context to suggest customizations
    if (context.selectedText) {
      suggestions.push({
        type: 'content',
        description: 'Add section for selected code context',
        modification: {
          type: 'add',
          target: '## 📋 Analysis',
          content: '\n### Selected Code Context\n```{{#if fileType}}{{fileType}}{{/if}}\n{{selectedText}}\n```\n',
          position: 0
        },
        confidence: 85
      })
    }

    if (context.fileType) {
      suggestions.push({
        type: 'condition',
        description: `Add specific handling for ${context.fileType} files`,
        modification: {
          type: 'add',
          target: '### Technical Considerations',
          content: `\n{{#if_eq fileType "${context.fileType}"}}\n### ${context.fileType.toUpperCase()} Specific Considerations\n- Follow ${context.fileType} best practices\n- Use appropriate tooling and linting\n- Consider performance implications\n{{/if}}\n`,
          position: 0
        },
        confidence: 70
      })
    }

    if (context.projectType) {
      suggestions.push({
        type: 'content',
        description: `Add project-specific guidance for ${context.projectType}`,
        modification: {
          type: 'replace',
          target: '{{#if_eq projectType "web"}}',
          content: `{{#if_eq projectType "${context.projectType}"}}\n### ${context.projectType.toUpperCase()} Development\n- Follow ${context.projectType} conventions\n- Use appropriate frameworks and tools\n- Consider deployment requirements\n{{else}}\n{{#if_eq projectType "web"}}`
        },
        confidence: 75
      })
    }

    // Check for frequently used patterns
    const usagePattern = this.usagePatterns.get(template.id)
    if (usagePattern) {
      if (usagePattern.commonModifications) {
        for (const commonMod of usagePattern.commonModifications) {
          suggestions.push({
            type: 'format',
            description: `Apply commonly used modification: ${commonMod.description}`,
            modification: commonMod,
            confidence: 60
          })
        }
      }
    }

    // Sort by confidence
    suggestions.sort((a, b) => b.confidence - a.confidence)

    return suggestions
  }

  async recordUsage (templateId: string, _context: TemplateContext, modifications?: TemplateModification[]): Promise<void> {
    try {
      const pattern = this.usagePatterns.get(templateId) || {
        usageCount: 0,
        contexts: [],
        commonModifications: []
      }

      pattern.usageCount++
      pattern.contexts.push({
        fileType: _context.fileType,
        projectType: _context.projectType,
        timestamp: new Date()
      })

      if (modifications) {
        for (const modification of modifications) {
          const existing = pattern.commonModifications.find((m: any) =>
            m.type === modification.type && m.target === modification.target
          )

          if (existing) {
            existing.count++
          } else {
            pattern.commonModifications.push({
              ...modification,
              count: 1,
              description: `${modification.type} ${modification.target}`
            })
          }
        }
      }

      // Keep only recent contexts (last 50)
      if (pattern.contexts.length > 50) {
        pattern.contexts = pattern.contexts.slice(-50)
      }

      this.usagePatterns.set(templateId, pattern)
      await this.saveUsagePatterns()

      this.logger.debug(`Recorded usage for template: ${templateId}`)
    } catch (error) {
      this.logger.warn('Failed to record template usage:', error)
    }
  }

  async getCustomization (id: string): Promise<TemplateCustomization | undefined> {
    return this.customizations.get(id)
  }

  async getAllCustomizations (): Promise<TemplateCustomization[]> {
    return Array.from(this.customizations.values())
  }

  async getCustomizationsForTemplate (templateId: string): Promise<TemplateCustomization[]> {
    return Array.from(this.customizations.values()).filter(c => c.templateId === templateId)
  }

  async deleteCustomization (id: string): Promise<boolean> {
    const deleted = this.customizations.delete(id)
    if (deleted) {
      await this.saveCustomizations()
      this.logger.info(`Deleted customization: ${id}`)
    }
    return deleted
  }

  async exportCustomizations (): Promise<string> {
    const customizations = Array.from(this.customizations.values())
    return JSON.stringify(customizations, null, 2)
  }

  async importCustomizations (data: string): Promise<number> {
    try {
      const customizations = JSON.parse(data) as TemplateCustomization[]
      let importedCount = 0

      for (const customization of customizations) {
        // Generate new ID to avoid conflicts
        const newId = `imported_${Date.now()}_${importedCount}`
        const importedCustomization = {
          ...customization,
          id: newId
        }

        this.customizations.set(newId, importedCustomization)
        importedCount++
      }

      await this.saveCustomizations()
      this.logger.info(`Imported ${importedCount} customizations`)
      return importedCount
    } catch (error) {
      this.logger.error('Failed to import customizations:', error)
      throw error
    }
  }

  private async saveCustomizations (): Promise<void> {
    await this.storageService.setGlobalValue(
      'templateCustomizations',
      Array.from(this.customizations.entries())
    )
  }

  private async saveUsagePatterns (): Promise<void> {
    await this.storageService.setGlobalValue(
      'templateUsagePatterns',
      Array.from(this.usagePatterns.entries())
    )
  }

  async getUsageStatistics (): Promise<any> {
    const stats = {
      totalCustomizations: this.customizations.size,
      customizationsByTemplate: {} as any,
      mostUsedModifications: [] as any[],
      recentActivity: [] as any[]
    }

    // Group by template
    for (const customization of this.customizations.values()) {
      if (!stats.customizationsByTemplate[customization.templateId]) {
        stats.customizationsByTemplate[customization.templateId] = 0
      }
      stats.customizationsByTemplate[customization.templateId]++
    }

    // Get most used modifications
    const modificationCounts = new Map<string, number>()
    for (const customization of this.customizations.values()) {
      for (const modification of customization.modifications) {
        const key = `${modification.type}:${modification.target}`
        modificationCounts.set(key, (modificationCounts.get(key) || 0) + 1)
      }
    }

    stats.mostUsedModifications = Array.from(modificationCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([key, count]) => ({ modification: key, count }))

    // Get recent activity
    stats.recentActivity = Array.from(this.customizations.values())
      .sort((a, b) => b.lastUsed.getTime() - a.lastUsed.getTime())
      .slice(0, 10)
      .map(c => ({
        id: c.id,
        name: c.name,
        templateId: c.templateId,
        lastUsed: c.lastUsed
      }))

    return stats
  }

  async clearCustomizations (): Promise<void> {
    this.customizations.clear()
    this.usagePatterns.clear()

    await this.saveCustomizations()
    await this.saveUsagePatterns()

    this.logger.info('Cleared all template customizations')
  }
}
