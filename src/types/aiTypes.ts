export interface AIProvider {
  name: string;
  generateResponse(prompt: string, context?: AIContext): Promise<AIResponse>;
  validateConfig(): Promise<boolean>;
  estimateTokens(text: string): number;
}

export interface AIContext {
  maxTokens?: number;
  temperature?: number;
  model?: string;
  systemPrompt?: string;
  conversationHistory?: AIMessage[];
  fileContext?: FileContext[];
  codeContext?: CodeContext[];
}

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  timestamp?: Date;
}

export interface AIResponse {
  content: string;
  tokensUsed?: number | undefined;
  model?: string | undefined;
  finishReason?: string | undefined;
  usage?: TokenUsage | undefined;
}

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

export interface FileContext {
  path: string;
  content: string;
  language?: string;
  relevanceScore?: number;
}

export interface CodeContext {
  type: 'function' | 'class' | 'interface' | 'variable' | 'import';
  name: string;
  content: string;
  location: {
    file: string;
    line: number;
    column: number;
  };
  relevanceScore?: number;
}

export interface AIConfig {
  provider: 'openai' | 'claude';
  model: string;
  apiKey: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export interface AIError {
  code: string;
  message: string;
  provider: string;
  retryable: boolean;
  details?: any;
}

export interface TokenOptimizationResult {
  optimizedPrompt: string;
  removedContext: string[];
  estimatedTokens: number;
  compressionRatio: number;
}

export enum AIProviderType {
  OPENAI = 'openai',
  CLAUDE = 'claude',
}

export enum AIModelType {
  GPT_4 = 'gpt-4',
  GPT_4_TURBO = 'gpt-4-turbo',
  GPT_4_1 = 'gpt-4o',
  CLAUDE_3_OPUS = 'claude-3-opus-20240229',
  CLAUDE_3_SONNET = 'claude-3-sonnet-20240229',
  CLAUDE_3_HAIKU = 'claude-3-haiku-20240307',
}

export interface RetryConfig {
  maxAttempts: number;
  initialDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryableErrors: string[];
}

export interface AIMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  totalTokensUsed: number;
  averageTokensPerRequest: number;
}
