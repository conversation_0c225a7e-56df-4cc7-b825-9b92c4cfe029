import * as path from 'path'
import { FileSystemService } from './fileSystemService'
import { FileFilter, FileFilterOptions } from '../utils/fileFilter'

export interface ProjectFile {
  path: string
  relativePath: string
  name: string
  extension: string
  size: number
  modifiedTime: Date
  type: 'file' | 'directory'
  isText: boolean
  language?: string
}

export interface ScanOptions {
  rootPath: string
  maxDepth?: number
  maxFiles?: number
  includeDirectories?: boolean
  filterOptions?: FileFilterOptions
}

export interface ScanResult {
  files: ProjectFile[]
  directories: ProjectFile[]
  totalFiles: number
  totalDirectories: number
  scanTime: number
  errors: string[]
}

export class ProjectScanner {
  private fileSystem: FileSystemService
  private fileFilter: FileFilter
  private scanErrors: string[] = []

  constructor(fileSystem: FileSystemService) {
    this.fileSystem = fileSystem
    this.fileFilter = new FileFilter()
  }

  async scan(options: ScanOptions): Promise<ScanResult> {
    const startTime = Date.now()
    this.scanErrors = []

    const filter = options.filterOptions ? 
      await FileFilter.createFromGitignore(options.rootPath, options.filterOptions) :
      await FileFilter.createFromGitignore(options.rootPath)

    this.fileFilter = filter

    const files: ProjectFile[] = []
    const directories: ProjectFile[] = []

    await this.scanDirectory(options.rootPath, options.rootPath, files, directories, options, 0)

    const scanTime = Date.now() - startTime

    return {
      files,
      directories,
      totalFiles: files.length,
      totalDirectories: directories.length,
      scanTime,
      errors: this.scanErrors
    }
  }

  private async scanDirectory(
    dirPath: string,
    rootPath: string,
    files: ProjectFile[],
    directories: ProjectFile[],
    options: ScanOptions,
    depth: number
  ): Promise<void> {
    if (options.maxDepth && depth > options.maxDepth) {
      return
    }

    if (options.maxFiles && files.length >= options.maxFiles) {
      return
    }

    try {
      const items = await this.fileSystem.readdir(dirPath)

      for (const item of items) {
        const itemPath = this.fileSystem.joinPath(dirPath, item)
        const relativePath = this.fileSystem.relative(rootPath, itemPath)

        if (!this.fileFilter.shouldInclude(relativePath)) {
          continue
        }

        try {
          const stats = await this.fileSystem.stat(itemPath)
          const isDirectory = stats.isDirectory()

          if (isDirectory) {
            if (options.includeDirectories) {
              directories.push({
                path: itemPath,
                relativePath,
                name: item,
                extension: '',
                size: 0,
                modifiedTime: stats.mtime,
                type: 'directory',
                isText: false
              })
            }

            await this.scanDirectory(itemPath, rootPath, files, directories, options, depth + 1)
          } else {
            const shouldInclude = await this.fileFilter.shouldIncludeFile(itemPath)
            
            if (shouldInclude) {
              const extension = this.fileSystem.extname(itemPath)
              const language = this.detectLanguage(extension)
              const isText = this.isTextFile(extension)

              files.push({
                path: itemPath,
                relativePath,
                name: item,
                extension,
                size: stats.size,
                modifiedTime: stats.mtime,
                type: 'file',
                isText,
                language
              })
            }
          }
        } catch (error) {
          this.scanErrors.push(`Error scanning ${itemPath}: ${error}`)
        }
      }
    } catch (error) {
      this.scanErrors.push(`Error reading directory ${dirPath}: ${error}`)
    }
  }

  private detectLanguage(extension: string): string | undefined {
    const languageMap: { [key: string]: string } = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.c': 'c',
      '.cpp': 'cpp',
      '.cc': 'cpp',
      '.cxx': 'cpp',
      '.h': 'c',
      '.hpp': 'cpp',
      '.cs': 'csharp',
      '.php': 'php',
      '.rb': 'ruby',
      '.go': 'go',
      '.rs': 'rust',
      '.swift': 'swift',
      '.kt': 'kotlin',
      '.scala': 'scala',
      '.sh': 'shell',
      '.bash': 'shell',
      '.zsh': 'shell',
      '.fish': 'shell',
      '.ps1': 'powershell',
      '.html': 'html',
      '.htm': 'html',
      '.css': 'css',
      '.scss': 'scss',
      '.sass': 'sass',
      '.less': 'less',
      '.json': 'json',
      '.xml': 'xml',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.toml': 'toml',
      '.ini': 'ini',
      '.cfg': 'ini',
      '.conf': 'ini',
      '.md': 'markdown',
      '.markdown': 'markdown',
      '.txt': 'text',
      '.sql': 'sql',
      '.r': 'r',
      '.R': 'r',
      '.m': 'matlab',
      '.pl': 'perl',
      '.lua': 'lua',
      '.vim': 'vim',
      '.dockerfile': 'dockerfile',
      '.dockerignore': 'dockerfile'
    }

    return languageMap[extension.toLowerCase()]
  }

  private isTextFile(extension: string): boolean {
    const textExtensions = [
      '.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.c', '.cpp', '.cc', '.cxx',
      '.h', '.hpp', '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
      '.sh', '.bash', '.zsh', '.fish', '.ps1', '.html', '.htm', '.css', '.scss',
      '.sass', '.less', '.json', '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg',
      '.conf', '.md', '.markdown', '.txt', '.sql', '.r', '.R', '.m', '.pl', '.lua',
      '.vim', '.dockerfile', '.dockerignore', '.gitignore', '.gitattributes',
      '.editorconfig', '.env', '.envrc', '.prettierrc', '.eslintrc', '.babelrc'
    ]

    return textExtensions.includes(extension.toLowerCase()) || extension === ''
  }

  async getProjectStatistics(rootPath: string): Promise<{
    totalFiles: number
    totalDirectories: number
    languageStats: { [language: string]: number }
    sizeStats: { totalSize: number, averageSize: number }
    extensionStats: { [extension: string]: number }
  }> {
    const scanResult = await this.scan({
      rootPath,
      includeDirectories: true,
      maxFiles: 50000
    })

    const languageStats: { [language: string]: number } = {}
    const extensionStats: { [extension: string]: number } = {}
    let totalSize = 0

    for (const file of scanResult.files) {
      if (file.language) {
        languageStats[file.language] = (languageStats[file.language] || 0) + 1
      }

      if (file.extension) {
        extensionStats[file.extension] = (extensionStats[file.extension] || 0) + 1
      }

      totalSize += file.size
    }

    return {
      totalFiles: scanResult.totalFiles,
      totalDirectories: scanResult.totalDirectories,
      languageStats,
      sizeStats: {
        totalSize,
        averageSize: scanResult.totalFiles > 0 ? totalSize / scanResult.totalFiles : 0
      },
      extensionStats
    }
  }

  async findFilesByPattern(rootPath: string, pattern: string): Promise<ProjectFile[]> {
    const scanResult = await this.scan({
      rootPath,
      filterOptions: {
        includePatterns: [pattern]
      }
    })

    return scanResult.files
  }

  async findFilesByExtension(rootPath: string, extensions: string[]): Promise<ProjectFile[]> {
    const scanResult = await this.scan({
      rootPath,
      filterOptions: {
        allowedExtensions: extensions
      }
    })

    return scanResult.files
  }

  async findFilesByLanguage(rootPath: string, language: string): Promise<ProjectFile[]> {
    const scanResult = await this.scan({ rootPath })
    return scanResult.files.filter(file => file.language === language)
  }

  async findRecentlyModified(rootPath: string, hours: number = 24): Promise<ProjectFile[]> {
    const scanResult = await this.scan({ rootPath })
    const cutoffTime = new Date(Date.now() - (hours * 60 * 60 * 1000))
    
    return scanResult.files
      .filter(file => file.modifiedTime > cutoffTime)
      .sort((a, b) => b.modifiedTime.getTime() - a.modifiedTime.getTime())
  }
}