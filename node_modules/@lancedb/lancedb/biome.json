{"$schema": "https://biomejs.dev/schemas/1.7.3/schema.json", "organizeImports": {"enabled": true}, "files": {"ignore": ["**/dist/**/*", "**/native.js", "**/native.d.ts", "**/npm/**/*", "**/.vscode/**"]}, "formatter": {"indentStyle": "space"}, "linter": {"enabled": true, "rules": {"recommended": false, "complexity": {"noBannedTypes": "error", "noExtraBooleanCast": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noUselessCatch": "error", "noUselessThisAlias": "error", "noUselessTypeConstraint": "error", "noWith": "error"}, "correctness": {"noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInnerDeclarations": "error", "noInvalidConstructorSuper": "error", "noNewSymbol": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedVariables": "warn", "useIsNan": "error", "useValidForDirection": "error", "useYield": "error"}, "style": {"noNamespace": "error", "useAsConstAssertion": "error", "useBlockStatements": "off", "useNamingConvention": {"level": "error", "options": {"strictCase": false}}}, "suspicious": {"noAssignInExpressions": "error", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noExplicitAny": "error", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useGetterReturn": "error", "useValidTypeof": "error"}}, "ignore": ["**/dist/**/*", "**/native.js", "**/native.d.ts"]}, "javascript": {"globals": []}, "overrides": [{"include": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts", "__test__/*.test.ts"], "linter": {"rules": {"correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidConstructorSuper": "off", "noNewSymbol": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"noArguments": "error", "noVar": "error", "useConst": "error"}, "suspicious": {"noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off"}}}}]}