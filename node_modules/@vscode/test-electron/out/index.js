"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VSCodeCommandError = exports.runVSCodeCommand = exports.resolveCliArgsFromVSCodeExecutablePath = exports.resolveCliPathFromVSCodeExecutablePath = exports.TestRunFailedError = exports.runTests = exports.downloadAndUnzipVSCode = exports.download = void 0;
var download_1 = require("./download");
Object.defineProperty(exports, "download", { enumerable: true, get: function () { return download_1.download; } });
Object.defineProperty(exports, "downloadAndUnzipVSCode", { enumerable: true, get: function () { return download_1.downloadAndUnzipVSCode; } });
var runTest_1 = require("./runTest");
Object.defineProperty(exports, "runTests", { enumerable: true, get: function () { return runTest_1.runTests; } });
Object.defineProperty(exports, "TestRunFailedError", { enumerable: true, get: function () { return runTest_1.TestRunFailedError; } });
var util_1 = require("./util");
Object.defineProperty(exports, "resolveCliPathFromVSCodeExecutablePath", { enumerable: true, get: function () { return util_1.resolveCliPathFromVSCodeExecutablePath; } });
Object.defineProperty(exports, "resolveCliArgsFromVSCodeExecutablePath", { enumerable: true, get: function () { return util_1.resolveCliArgsFromVSCodeExecutablePath; } });
Object.defineProperty(exports, "runVSCodeCommand", { enumerable: true, get: function () { return util_1.runVSCodeCommand; } });
Object.defineProperty(exports, "VSCodeCommandError", { enumerable: true, get: function () { return util_1.VSCodeCommandError; } });
__exportStar(require("./progress.js"), exports);
