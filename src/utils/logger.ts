import * as vscode from 'vscode'

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export class Logger {
  private readonly outputChannel: vscode.OutputChannel
  private logLevel: LogLevel = LogLevel.INFO

  constructor (private readonly name: string) {
    this.outputChannel = vscode.window.createOutputChannel(name)
  }

  setLogLevel (level: LogLevel): void {
    this.logLevel = level
  }

  debug (message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.DEBUG) {
      this.log('DEBUG', message, ...args)
    }
  }

  info (message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.INFO) {
      this.log('INFO', message, ...args)
    }
  }

  warn (message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.WARN) {
      this.log('WARN', message, ...args)
    }
  }

  error (message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.ERROR) {
      this.log('ERROR', message, ...args)
    }
  }

  private log (level: string, message: string, ...args: any[]): void {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [${level}] ${message}`

    if (args.length > 0) {
      const formattedArgs = args
        .map((arg) => {
          if (typeof arg === 'object') {
            return JSON.stringify(arg, null, 2)
          }
          return String(arg)
        })
        .join(' ')

      this.outputChannel.appendLine(`${logMessage} ${formattedArgs}`)
    } else {
      this.outputChannel.appendLine(logMessage)
    }

    // Also log to console in development
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log(`[${this.name}] ${logMessage}`, ...args)
    }
  }

  show (): void {
    this.outputChannel.show()
  }

  hide (): void {
    this.outputChannel.hide()
  }

  dispose (): void {
    this.outputChannel.dispose()
  }
}
