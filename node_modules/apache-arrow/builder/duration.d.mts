import { FixedWidthBuilder } from '../builder.js';
import { Duration, DurationSecond, DurationMillisecond, DurationMicrosecond, DurationNanosecond } from '../type.js';
/** @ignore */
export declare class DurationBuilder<T extends Duration = Duration, TNull = any> extends FixedWidthBuilder<T, TNull> {
}
/** @ignore */
export declare class DurationSecondBuilder<TNull = any> extends DurationBuilder<DurationSecond, TNull> {
}
/** @ignore */
export declare class DurationMillisecondBuilder<TNull = any> extends DurationBuilder<DurationMillisecond, TNull> {
}
/** @ignore */
export declare class DurationMicrosecondBuilder<TNull = any> extends DurationBuilder<DurationMicrosecond, TNull> {
}
/** @ignore */
export declare class DurationNanosecondBuilder<TNull = any> extends DurationBuilder<DurationNanosecond, TNull> {
}
