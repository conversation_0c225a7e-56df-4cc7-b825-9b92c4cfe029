{"version": 3, "sources": ["fb/floating-point.ts"], "names": [], "mappings": ";AAAA,qEAAqE;;;AAErE,2CAA2C;AAE3C,iDAA2C;AAG3C,MAAa,aAAa;IAA1B;QACE,OAAE,GAAgC,IAAI,CAAC;QACvC,WAAM,GAAG,CAAC,CAAC;IAuCb,CAAC;IAtCC,MAAM,CAAC,CAAQ,EAAE,EAAyB;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,sBAAsB,CAAC,EAAyB,EAAE,GAAkB;QACzE,OAAO,CAAC,GAAG,IAAI,IAAI,aAAa,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9F,CAAC;IAED,MAAM,CAAC,kCAAkC,CAAC,EAAyB,EAAE,GAAkB;QACrF,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,aAAa,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9F,CAAC;IAED,SAAS;QACP,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,wBAAS,CAAC,IAAI,CAAC;IAC5E,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,OAA2B;QACnD,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAA2B,EAAE,SAAmB;QAClE,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,SAAS,EAAE,wBAAS,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,OAA2B;QACjD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,mBAAmB,CAAC,OAA2B,EAAE,SAAmB;QACzE,aAAa,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC1C,aAAa,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC/C,OAAO,aAAa,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;CACA;AAzCD,sCAyCC", "file": "floating-point.js", "sourceRoot": "../src"}