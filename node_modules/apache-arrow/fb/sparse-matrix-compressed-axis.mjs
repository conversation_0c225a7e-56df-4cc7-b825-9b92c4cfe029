// automatically generated by the FlatBuffers compiler, do not modify
export var SparseMatrixCompressedAxis;
(function (SparseMatrixCompressedAxis) {
    SparseMatrixCompressedAxis[SparseMatrixCompressedAxis["Row"] = 0] = "Row";
    SparseMatrixCompressedAxis[SparseMatrixCompressedAxis["Column"] = 1] = "Column";
})(SparseMatrixCompressedAxis || (SparseMatrixCompressedAxis = {}));

//# sourceMappingURL=sparse-matrix-compressed-axis.mjs.map
