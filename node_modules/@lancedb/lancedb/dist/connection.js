"use strict";
// Copyright 2024 Lance Developers.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Connection = exports.connect = void 0;
const arrow_1 = require("./arrow");
const registry_1 = require("./embedding/registry");
const native_1 = require("./native");
const table_1 = require("./table");
/**
 * Connect to a LanceDB instance at the given URI.
 *
 * Accepted formats:
 *
 * - `/path/to/database` - local database
 * - `s3://bucket/path/to/database` or `gs://bucket/path/to/database` - database on cloud storage
 * - `db://host:port` - remote database (LanceDB cloud)
 * @param {string} uri - The uri of the database. If the database uri starts
 * with `db://` then it connects to a remote database.
 * @see {@link ConnectionOptions} for more details on the URI format.
 */
async function connect(uri, opts) {
    opts = opts ?? {};
    opts.storageOptions = cleanseStorageOptions(opts.storageOptions);
    const nativeConn = await native_1.Connection.new(uri, opts);
    return new Connection(nativeConn);
}
exports.connect = connect;
/**
 * A LanceDB Connection that allows you to open tables and create new ones.
 *
 * Connection could be local against filesystem or remote against a server.
 *
 * A Connection is intended to be a long lived object and may hold open
 * resources such as HTTP connection pools.  This is generally fine and
 * a single connection should be shared if it is going to be used many
 * times. However, if you are finished with a connection, you may call
 * close to eagerly free these resources.  Any call to a Connection
 * method after it has been closed will result in an error.
 *
 * Closing a connection is optional.  Connections will automatically
 * be closed when they are garbage collected.
 *
 * Any created tables are independent and will continue to work even if
 * the underlying connection has been closed.
 */
class Connection {
    inner;
    constructor(inner) {
        this.inner = inner;
    }
    /** Return true if the connection has not been closed */
    isOpen() {
        return this.inner.isOpen();
    }
    /**
     * Close the connection, releasing any underlying resources.
     *
     * It is safe to call this method multiple times.
     *
     * Any attempt to use the connection after it is closed will result in an error.
     */
    close() {
        this.inner.close();
    }
    /** Return a brief description of the connection */
    display() {
        return this.inner.display();
    }
    /**
     * List all the table names in this database.
     *
     * Tables will be returned in lexicographical order.
     * @param {Partial<TableNamesOptions>} options - options to control the
     * paging / start point
     */
    async tableNames(options) {
        return this.inner.tableNames(options?.startAfter, options?.limit);
    }
    /**
     * Open a table in the database.
     * @param {string} name - The name of the table
     */
    async openTable(name, options) {
        const innerTable = await this.inner.openTable(name, cleanseStorageOptions(options?.storageOptions), options?.indexCacheSize);
        return new table_1.Table(innerTable);
    }
    /**
     * Creates a new Table and initialize it with new data.
     * @param {string} name - The name of the table.
     * @param {Record<string, unknown>[] | ArrowTable} data - Non-empty Array of Records
     * to be inserted into the table
     */
    async createTable(name, data, options) {
        let mode = options?.mode ?? "create";
        const existOk = options?.existOk ?? false;
        if (mode === "create" && existOk) {
            mode = "exist_ok";
        }
        let table;
        if ((0, arrow_1.isArrowTable)(data)) {
            table = data;
        }
        else {
            table = (0, arrow_1.makeArrowTable)(data, options);
        }
        const buf = await (0, arrow_1.fromTableToBuffer)(table, options?.embeddingFunction, options?.schema);
        const innerTable = await this.inner.createTable(name, buf, mode, cleanseStorageOptions(options?.storageOptions), options?.useLegacyFormat);
        return new table_1.Table(innerTable);
    }
    /**
     * Creates a new empty Table
     * @param {string} name - The name of the table.
     * @param {Schema} schema - The schema of the table
     */
    async createEmptyTable(name, schema, options) {
        let mode = options?.mode ?? "create";
        const existOk = options?.existOk ?? false;
        if (mode === "create" && existOk) {
            mode = "exist_ok";
        }
        let metadata = undefined;
        if (options?.embeddingFunction !== undefined) {
            const embeddingFunction = options.embeddingFunction;
            const registry = (0, registry_1.getRegistry)();
            metadata = registry.getTableMetadata([embeddingFunction]);
        }
        const table = (0, arrow_1.makeEmptyTable)(schema, metadata);
        const buf = await (0, arrow_1.fromTableToBuffer)(table);
        const innerTable = await this.inner.createEmptyTable(name, buf, mode, cleanseStorageOptions(options?.storageOptions), options?.useLegacyFormat);
        return new table_1.Table(innerTable);
    }
    /**
     * Drop an existing table.
     * @param {string} name The name of the table to drop.
     */
    async dropTable(name) {
        return this.inner.dropTable(name);
    }
}
exports.Connection = Connection;
/**
 * Takes storage options and makes all the keys snake case.
 */
function cleanseStorageOptions(options) {
    if (options === undefined) {
        return undefined;
    }
    const result = {};
    for (const [key, value] of Object.entries(options)) {
        if (value !== undefined) {
            const newKey = camelToSnakeCase(key);
            result[newKey] = value;
        }
    }
    return result;
}
/**
 * Convert a string to snake case. It might already be snake case, in which case it is
 * returned unchanged.
 */
function camelToSnakeCase(camel) {
    if (camel.includes("_")) {
        // Assume if there is at least one underscore, it is already snake case
        return camel;
    }
    if (camel.toLocaleUpperCase() === camel) {
        // Assume if the string is all uppercase, it is already snake case
        return camel;
    }
    let result = camel.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
    if (result.startsWith("_")) {
        result = result.slice(1);
    }
    return result;
}
