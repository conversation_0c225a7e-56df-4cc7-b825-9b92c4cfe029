{"name": "@lancedb/lancedb", "version": "0.5.2", "main": "dist/index.js", "exports": {".": "./dist/index.js", "./embedding": "./dist/embedding/index.js"}, "types": "dist/index.d.ts", "napi": {"name": "lancedb", "triples": {"defaults": false, "additional": ["aarch64-apple-darwin", "aarch64-unknown-linux-gnu", "x86_64-apple-darwin", "x86_64-unknown-linux-gnu", "x86_64-pc-windows-msvc"]}}, "license": "Apache 2.0", "devDependencies": {"@aws-sdk/client-kms": "^3.33.0", "@aws-sdk/client-s3": "^3.33.0", "@biomejs/biome": "^1.7.3", "@jest/globals": "^29.7.0", "@napi-rs/cli": "^2.18.0", "@types/jest": "^29.1.2", "@types/tmp": "^0.2.6", "apache-arrow-old": "npm:apache-arrow@13.0.0", "eslint": "^8.57.0", "jest": "^29.7.0", "shx": "^0.3.4", "tmp": "^0.2.3", "ts-jest": "^29.1.2", "typedoc": "^0.25.7", "typedoc-plugin-markdown": "^3.17.1", "typescript": "^5.3.3", "typescript-eslint": "^7.1.0"}, "ava": {"timeout": "3m"}, "engines": {"node": ">= 18"}, "cpu": ["x64", "arm64"], "os": ["darwin", "linux", "win32"], "scripts": {"artifacts": "napi artifacts", "build:debug": "napi build --platform --dts ../lancedb/native.d.ts --js ../lancedb/native.js lancedb", "build:release": "napi build --platform --release --dts ../lancedb/native.d.ts --js ../lancedb/native.js dist/", "build": "npm run build:debug && tsc -b && shx cp lancedb/native.d.ts dist/native.d.ts && shx cp lancedb/*.node dist/", "build-release": "npm run build:release && tsc -b && shx cp lancedb/native.d.ts dist/native.d.ts", "lint-ci": "biome ci .", "docs": "typedoc --plugin typedoc-plugin-markdown --out ../docs/src/js lancedb/index.ts", "lint": "biome check . && biome format .", "lint-fix": "biome check --apply-unsafe  . && biome format --write .", "prepublishOnly": "napi prepublish -t npm", "test": "jest --verbose", "integration": "S3_TEST=1 npm run test", "universal": "napi universal", "version": "napi version"}, "dependencies": {"apache-arrow": "^15.0.0", "openai": "^4.29.2", "reflect-metadata": "^0.2.2"}, "optionalDependencies": {"@lancedb/lancedb-darwin-arm64": "0.5.2", "@lancedb/lancedb-linux-arm64-gnu": "0.5.2", "@lancedb/lancedb-darwin-x64": "0.5.2", "@lancedb/lancedb-linux-x64-gnu": "0.5.2", "@lancedb/lancedb-win32-x64-msvc": "0.5.2"}}