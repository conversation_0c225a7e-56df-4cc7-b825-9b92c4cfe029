import * as Handlebars from 'handlebars'
import * as vscode from 'vscode'
import * as path from 'path'
import * as fs from 'fs'
import { Logger } from '../utils/logger'
import { StorageService } from './storageService'

export interface TemplateContext {
  input: string;
  fileType?: string;
  fileName?: string;
  projectType?: string;
  selectedText?: string;
  cursorPosition?: vscode.Position;
  activeFile?: string;
  workspacePath?: string;
  userPreferences?: any;
  codeContext?: any[];
  fileContext?: any[];
  timestamp?: Date;
}

export interface Template {
  id: string;
  name: string;
  description: string;
  content: string;
  category: string;
  fileTypes: string[];
  projectTypes: string[];
  priority: number;
  version: string;
  author: string;
  lastModified: Date;
}

export interface TemplateMatch {
  template: Template;
  score: number;
  reasons: string[];
}

export class TemplateEngine {
  private readonly handlebars: typeof Handlebars
  private readonly templates: Map<string, Template> = new Map()
  private readonly templateCache: Map<string, HandlebarsTemplateDelegate> = new Map()

  constructor (private readonly logger: Logger, private readonly storageService: StorageService, private readonly extensionPath: string) {
    this.handlebars = Handlebars.create()
    this.registerHelpers()
  }

  async initialize (): Promise<void> {
    try {
      this.logger.info('Initializing Template Engine...')

      await this.loadBuiltinTemplates()
      await this.loadCustomTemplates()

      this.logger.info(`Template Engine initialized with ${this.templates.size} templates`)
    } catch (error) {
      this.logger.error('Failed to initialize Template Engine:', error)
      throw error
    }
  }

  private registerHelpers (): void {
    // Date helpers
    this.handlebars.registerHelper('now', () => new Date().toISOString())
    this.handlebars.registerHelper('date', (format: string) => {
      const date = new Date()
      switch (format) {
        case 'short':
          return date.toLocaleDateString()
        case 'long':
          return date.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })
        default:
          return date.toISOString()
      }
    })

    // String helpers
    this.handlebars.registerHelper('uppercase', (str: string) => str?.toUpperCase() || '')
    this.handlebars.registerHelper('lowercase', (str: string) => str?.toLowerCase() || '')
    this.handlebars.registerHelper('capitalize', (str: string) => {
      if (!str) {
        return ''
      }
      return str.charAt(0).toUpperCase() + str.slice(1)
    })
    this.handlebars.registerHelper('truncate', (str: string, length: number) => {
      if (!str) {
        return ''
      }
      return str.length > length ? str.substring(0, length) + '...' : str
    })

    // Conditional helpers
    this.handlebars.registerHelper('if_eq', function (this: any, a: any, b: any, options: any) {
      if (a === b) {
        return options.fn(this)
      }
      return options.inverse(this)
    })

    this.handlebars.registerHelper('if_contains', function (this: any, str: string, substring: string, options: any) {
      if (str && str.includes(substring)) {
        return options.fn(this)
      }
      return options.inverse(this)
    })

    // Array helpers
    this.handlebars.registerHelper('each_with_index', function (array: any[], options: any) {
      let result = ''
      for (let i = 0; i < array.length; i++) {
        result += options.fn({ ...array[i], index: i, first: i === 0, last: i === array.length - 1 })
      }
      return result
    })

    // Code helpers
    this.handlebars.registerHelper('code_block', function (code: string, language: string = '') {
      return `\`\`\`${language}\n${code}\n\`\`\``
    })

    this.handlebars.registerHelper('file_extension', function (filename: string) {
      return path.extname(filename).slice(1)
    })

    this.handlebars.registerHelper('file_name', function (filepath: string) {
      return path.basename(filepath)
    })

    // Project type helpers
    this.handlebars.registerHelper('is_react_project', function (projectType: string) {
      return projectType === 'react' || projectType === 'next' || projectType === 'gatsby'
    })

    this.handlebars.registerHelper('is_node_project', function (projectType: string) {
      return projectType === 'node' || projectType === 'express' || projectType === 'nest'
    })

    this.handlebars.registerHelper('is_python_project', function (projectType: string) {
      return projectType === 'python' || projectType === 'django' || projectType === 'flask'
    })
  }

  private async loadBuiltinTemplates (): Promise<void> {
    const templatesPath = path.join(this.extensionPath, 'src', 'templates')

    try {
      const templateFiles = await fs.promises.readdir(templatesPath)

      for (const file of templateFiles) {
        if (file.endsWith('.hbs')) {
          const templatePath = path.join(templatesPath, file)
          const content = await fs.promises.readFile(templatePath, 'utf-8')

          const template: Template = {
            id: path.basename(file, '.hbs'),
            name: this.extractTemplateName(content),
            description: this.extractTemplateDescription(content),
            content: content,
            category: this.extractTemplateCategory(content),
            fileTypes: this.extractTemplateFileTypes(content),
            projectTypes: this.extractTemplateProjectTypes(content),
            priority: this.extractTemplatePriority(content),
            version: '1.0.0',
            author: 'Built-in',
            lastModified: new Date(),
          }

          this.templates.set(template.id, template)
          this.logger.debug(`Loaded builtin template: ${template.id}`)
        }
      }
    } catch (error) {
      this.logger.warn('Failed to load builtin templates:', error)
    }
  }

  private async loadCustomTemplates (): Promise<void> {
    try {
      const customTemplates = await this.storageService.getGlobalValue<Template[]>('customTemplates', [])

      if (customTemplates) {
        for (const template of customTemplates) {
          this.templates.set(template.id, template)
          this.logger.debug(`Loaded custom template: ${template.id}`)
        }
      }
    } catch (error) {
      this.logger.warn('Failed to load custom templates:', error)
    }
  }

  async renderTemplate (templateId: string, context: TemplateContext): Promise<string> {
    const template = this.templates.get(templateId)
    if (!template) {
      throw new Error(`Template not found: ${templateId}`)
    }

    try {
      // Get or create compiled template
      let compiledTemplate = this.templateCache.get(templateId)
      if (!compiledTemplate) {
        compiledTemplate = this.handlebars.compile(template.content)
        this.templateCache.set(templateId, compiledTemplate)
      }

      // Render with context
      const result = compiledTemplate(context)

      this.logger.debug(`Template rendered: ${templateId}`)
      return result
    } catch (error) {
      this.logger.error(`Failed to render template ${templateId}:`, error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      throw new Error(`Template rendering failed: ${errorMessage}`)
    }
  }

  selectBestTemplate (context: TemplateContext): TemplateMatch | null {
    const matches: TemplateMatch[] = []

    for (const template of this.templates.values()) {
      const score = this.calculateTemplateScore(template, context)
      if (score > 0) {
        matches.push({
          template,
          score,
          reasons: this.getMatchReasons(template, context),
        })
      }
    }

    // Sort by score (highest first)
    matches.sort((a, b) => b.score - a.score)

    return matches.length > 0 ? matches[0] || null : null
  }

  private calculateTemplateScore (template: Template, context: TemplateContext): number {
    let score = 0

    // Base priority
    score += template.priority

    // File type match
    if (context.fileType && template.fileTypes.includes(context.fileType)) {
      score += 10
    }

    // Project type match
    if (context.projectType && template.projectTypes.includes(context.projectType)) {
      score += 15
    }

    // Category relevance
    if (context.input) {
      const inputLower = context.input.toLowerCase()
      const categoryLower = template.category.toLowerCase()

      if (inputLower.includes(categoryLower)) {
        score += 5
      }
    }

    // Content relevance
    if (context.selectedText && template.content.includes('selectedText')) {
      score += 8
    }

    if (context.codeContext && template.content.includes('codeContext')) {
      score += 6
    }

    if (context.fileContext && template.content.includes('fileContext')) {
      score += 4
    }

    return score
  }

  private getMatchReasons (template: Template, context: TemplateContext): string[] {
    const reasons: string[] = []

    if (context.fileType && template.fileTypes.includes(context.fileType)) {
      reasons.push(`Matches file type: ${context.fileType}`)
    }

    if (context.projectType && template.projectTypes.includes(context.projectType)) {
      reasons.push(`Matches project type: ${context.projectType}`)
    }

    if (context.input) {
      const inputLower = context.input.toLowerCase()
      const categoryLower = template.category.toLowerCase()

      if (inputLower.includes(categoryLower)) {
        reasons.push(`Matches category: ${template.category}`)
      }
    }

    if (template.priority > 5) {
      reasons.push('High priority template')
    }

    return reasons
  }

  async addCustomTemplate (template: Omit<Template, 'id' | 'lastModified'>): Promise<string> {
    const id = `custom_${Date.now()}`
    const fullTemplate: Template = {
      ...template,
      id,
      lastModified: new Date(),
    }

    this.templates.set(id, fullTemplate)

    // Save to storage
    const customTemplates = await this.storageService.getGlobalValue<Template[]>('customTemplates', [])
    if (customTemplates) {
      customTemplates.push(fullTemplate)
      await this.storageService.setGlobalValue('customTemplates', customTemplates)
    }

    this.logger.info(`Added custom template: ${id}`)
    return id
  }

  async removeCustomTemplate (templateId: string): Promise<boolean> {
    const template = this.templates.get(templateId)
    if (!template || !templateId.startsWith('custom_')) {
      return false
    }

    this.templates.delete(templateId)
    this.templateCache.delete(templateId)

    // Remove from storage
    const customTemplates = await this.storageService.getGlobalValue<Template[]>('customTemplates', [])
    if (customTemplates) {
      const filtered = customTemplates.filter((t) => t.id !== templateId)
      await this.storageService.setGlobalValue('customTemplates', filtered)
    }

    this.logger.info(`Removed custom template: ${templateId}`)
    return true
  }

  getAllTemplates (): Template[] {
    return Array.from(this.templates.values())
  }

  getTemplate (id: string): Template | undefined {
    return this.templates.get(id)
  }

  clearCache (): void {
    this.templateCache.clear()
    this.logger.info('Template cache cleared')
  }

  // Helper methods for extracting template metadata from content
  private extractTemplateName (content: string): string {
    const match = content.match(/{{!--\s*name:\s*(.+?)\s*--}}/)
    return match && match[1] ? match[1] : 'Unnamed Template'
  }

  private extractTemplateDescription (content: string): string {
    const match = content.match(/{{!--\s*description:\s*(.+?)\s*--}}/)
    return match && match[1] ? match[1] : 'No description'
  }

  private extractTemplateCategory (content: string): string {
    const match = content.match(/{{!--\s*category:\s*(.+?)\s*--}}/)
    return match && match[1] ? match[1] : 'general'
  }

  private extractTemplateFileTypes (content: string): string[] {
    const match = content.match(/{{!--\s*fileTypes:\s*(.+?)\s*--}}/)
    return match && match[1] ? match[1].split(',').map((s) => s.trim()) : []
  }

  private extractTemplateProjectTypes (content: string): string[] {
    const match = content.match(/{{!--\s*projectTypes:\s*(.+?)\s*--}}/)
    return match && match[1] ? match[1].split(',').map((s) => s.trim()) : []
  }

  private extractTemplatePriority (content: string): number {
    const match = content.match(/{{!--\s*priority:\s*(\d+)\s*--}}/)
    return match && match[1] ? parseInt(match[1]) : 1
  }
}
