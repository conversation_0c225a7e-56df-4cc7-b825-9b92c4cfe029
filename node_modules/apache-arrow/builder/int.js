"use strict";
// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Uint64Builder = exports.Uint32Builder = exports.Uint16Builder = exports.Uint8Builder = exports.Int64Builder = exports.Int32Builder = exports.Int16Builder = exports.Int8Builder = exports.IntBuilder = void 0;
const builder_js_1 = require("../builder.js");
/** @ignore */
class IntBuilder extends builder_js_1.FixedWidthBuilder {
    setValue(index, value) {
        this._values.set(index, value);
    }
}
exports.IntBuilder = IntBuilder;
/** @ignore */
class Int8Builder extends IntBuilder {
}
exports.Int8Builder = Int8Builder;
/** @ignore */
class Int16Builder extends IntBuilder {
}
exports.Int16Builder = Int16Builder;
/** @ignore */
class Int32Builder extends IntBuilder {
}
exports.Int32Builder = Int32Builder;
/** @ignore */
class Int64Builder extends IntBuilder {
}
exports.Int64Builder = Int64Builder;
/** @ignore */
class Uint8Builder extends IntBuilder {
}
exports.Uint8Builder = Uint8Builder;
/** @ignore */
class Uint16Builder extends IntBuilder {
}
exports.Uint16Builder = Uint16Builder;
/** @ignore */
class Uint32Builder extends IntBuilder {
}
exports.Uint32Builder = Uint32Builder;
/** @ignore */
class Uint64Builder extends IntBuilder {
}
exports.Uint64Builder = Uint64Builder;

//# sourceMappingURL=int.js.map
