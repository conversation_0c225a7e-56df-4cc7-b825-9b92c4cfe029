{"version": 3, "sources": ["fb/dictionary-batch.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,WAAW,MAAM,aAAa,CAAC;AAE3C,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAGhD;;;;;;;GAOG;AACH,qBAAa,eAAe;IAC1B,EAAE,EAAE,WAAW,CAAC,UAAU,GAAC,IAAI,CAAQ;IACvC,MAAM,SAAK;IACX,MAAM,CAAC,CAAC,EAAC,MAAM,EAAE,EAAE,EAAC,WAAW,CAAC,UAAU,GAAE,eAAe;IAM7D,MAAM,CAAC,wBAAwB,CAAC,EAAE,EAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAAC,eAAe,GAAE,eAAe;IAIhG,MAAM,CAAC,oCAAoC,CAAC,EAAE,EAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAAC,eAAe,GAAE,eAAe;IAK5G,EAAE,IAAG,MAAM;IAKX,IAAI,CAAC,GAAG,CAAC,EAAC,WAAW,GAAE,WAAW,GAAC,IAAI;IAKvC;;;;OAIG;IACH,OAAO,IAAG,OAAO;IAKjB,MAAM,CAAC,oBAAoB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO;IAIvD,MAAM,CAAC,KAAK,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,EAAE,EAAC,MAAM;IAInD,MAAM,CAAC,OAAO,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,UAAU,EAAC,WAAW,CAAC,MAAM;IAIzE,MAAM,CAAC,UAAU,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAC,OAAO;IAI9D,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,GAAE,WAAW,CAAC,MAAM;CAKxE", "file": "dictionary-batch.d.ts", "sourceRoot": "../src"}