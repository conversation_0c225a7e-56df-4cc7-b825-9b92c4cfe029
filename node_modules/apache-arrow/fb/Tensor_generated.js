"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.unionListToType = exports.unionToType = exports.Type = exports.TensorDim = exports.Tensor = exports.Buffer = void 0;
var buffer_js_1 = require("./buffer.js");
Object.defineProperty(exports, "Buffer", { enumerable: true, get: function () { return buffer_js_1.<PERSON><PERSON><PERSON>; } });
var tensor_js_1 = require("./tensor.js");
Object.defineProperty(exports, "Tensor", { enumerable: true, get: function () { return tensor_js_1.Tensor; } });
var tensor_dim_js_1 = require("./tensor-dim.js");
Object.defineProperty(exports, "TensorDim", { enumerable: true, get: function () { return tensor_dim_js_1.TensorDim; } });
var type_js_1 = require("./type.js");
Object.defineProperty(exports, "Type", { enumerable: true, get: function () { return type_js_1.Type; } });
Object.defineProperty(exports, "unionToType", { enumerable: true, get: function () { return type_js_1.unionToType; } });
Object.defineProperty(exports, "unionListToType", { enumerable: true, get: function () { return type_js_1.unionListToType; } });

//# sourceMappingURL=Tensor_generated.js.map
