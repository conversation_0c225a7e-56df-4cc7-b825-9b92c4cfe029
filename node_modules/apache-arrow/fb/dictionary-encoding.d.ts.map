{"version": 3, "sources": ["fb/dictionary-encoding.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,WAAW,MAAM,aAAa,CAAC;AAE3C,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACtD,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAG/B,qBAAa,kBAAkB;IAC7B,EAAE,EAAE,WAAW,CAAC,UAAU,GAAC,IAAI,CAAQ;IACvC,MAAM,SAAK;IACX,MAAM,CAAC,CAAC,EAAC,MAAM,EAAE,EAAE,EAAC,WAAW,CAAC,UAAU,GAAE,kBAAkB;IAMhE,MAAM,CAAC,2BAA2B,CAAC,EAAE,EAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAAC,kBAAkB,GAAE,kBAAkB;IAIzG,MAAM,CAAC,uCAAuC,CAAC,EAAE,EAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAAC,kBAAkB,GAAE,kBAAkB;IAKrH;;;;OAIG;IACH,EAAE,IAAG,MAAM;IAKX;;;;;;OAMG;IACH,SAAS,CAAC,GAAG,CAAC,EAAC,GAAG,GAAE,GAAG,GAAC,IAAI;IAK5B;;;;;OAKG;IACH,SAAS,IAAG,OAAO;IAKnB,cAAc,IAAG,cAAc;IAK/B,MAAM,CAAC,uBAAuB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO;IAI1D,MAAM,CAAC,KAAK,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,EAAE,EAAC,MAAM;IAInD,MAAM,CAAC,YAAY,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAC,WAAW,CAAC,MAAM;IAInF,MAAM,CAAC,YAAY,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,SAAS,EAAC,OAAO;IAIlE,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,cAAc,EAAC,cAAc;IAInF,MAAM,CAAC,qBAAqB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,GAAE,WAAW,CAAC,MAAM;CAK3E", "file": "dictionary-encoding.d.ts", "sourceRoot": "../src"}