import * as vscode from 'vscode'
import { Logger } from '../utils/logger'
import { StorageService } from '../services/storageService'

export class ConfigureCommand {
  constructor (
    private readonly _context: vscode.ExtensionContext,
    private readonly logger: Logger,
    private readonly storageService: StorageService
  ) {}

  async execute (..._args: any[]): Promise<void> {
    try {
      this.logger.info('Configure command executed')

      const options = [
        'Set API Key',
        'Configure AI Provider',
        'Adjust Settings',
        'View Index Status',
        'Reset Configuration'
      ]

      const selected = await vscode.window.showQuickPick(options, {
        title: 'Task Transformer Configuration',
        placeHolder: 'Select a configuration option'
      })

      if (!selected) {
        return
      }

      switch (selected) {
        case 'Set API Key':
          await this.setApiKey()
          break
        case 'Configure AI Provider':
          await this.configureAiProvider()
          break
        case 'Adjust Settings':
          await this.adjustSettings()
          break
        case 'View Index Status':
          await this.viewIndexStatus()
          break
        case 'Reset Configuration':
          await this.resetConfiguration()
          break
      }

    } catch (error) {
      this.logger.error('Configure command failed:', error)
      vscode.window.showErrorMessage('Failed to configure extension.')
    }
  }

  private async setApiKey (): Promise<void> {
    const apiKey = await vscode.window.showInputBox({
      prompt: 'Enter your API Key',
      password: true,
      placeHolder: 'sk-...'
    })

    if (apiKey) {
      await this.storageService.storeSecret('apiKey', apiKey)
      vscode.window.showInformationMessage('API Key saved successfully')
    }
  }

  private async configureAiProvider (): Promise<void> {
    const providers = ['OpenAI', 'Claude']
    const selected = await vscode.window.showQuickPick(providers, {
      title: 'Select AI Provider',
      placeHolder: 'Choose your preferred AI provider'
    })

    if (selected) {
      const config = vscode.workspace.getConfiguration('taskTransformer')
      await config.update('aiProvider', selected.toLowerCase(), vscode.ConfigurationTarget.Global)
      vscode.window.showInformationMessage(`AI Provider set to: ${selected}`)
    }
  }

  private async adjustSettings (): Promise<void> {
    // Open settings UI
    await vscode.commands.executeCommand('workbench.action.openSettings', 'taskTransformer')
  }

  private async viewIndexStatus (): Promise<void> {
    // TODO: Implement index status view
    vscode.window.showInformationMessage('Index Status: Not implemented yet')
  }

  private async resetConfiguration (): Promise<void> {
    const confirm = await vscode.window.showWarningMessage(
      'Are you sure you want to reset all configuration?',
      'Yes',
      'No'
    )

    if (confirm === 'Yes') {
      // TODO: Implement configuration reset
      vscode.window.showInformationMessage('Configuration reset (placeholder)')
    }
  }
}
