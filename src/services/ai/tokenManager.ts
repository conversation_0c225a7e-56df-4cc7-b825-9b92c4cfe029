import { AIContext, TokenOptim<PERSON><PERSON><PERSON>ult, FileContext, CodeContext } from '../../types/aiTypes'

export class TokenManager {
  private readonly CHARS_PER_TOKEN = 4 // Rough estimation
  private readonly MAX_CONTEXT_RATIO = 0.7 // Use 70% of max tokens for context

  estimateTokens (text: string): number {
    return Math.ceil(text.length / this.CHARS_PER_TOKEN)
  }

  async optimizeContext (prompt: string, context?: AIContext): Promise<AIContext> {
    if (!context) {
      return {}
    }

    const maxTokens = context.maxTokens || 4000
    const promptTokens = this.estimateTokens(prompt)
    const availableTokens = Math.floor(maxTokens * this.MAX_CONTEXT_RATIO) - promptTokens

    if (availableTokens <= 0) {
      return {
        ...context,
        fileContext: [],
        codeContext: [],
        conversationHistory: []
      }
    }

    let optimizedContext: AIContext = { ...context }
    let currentTokens = 0

    // Optimize system prompt
    if (context.systemPrompt) {
      const systemTokens = this.estimateTokens(context.systemPrompt)
      if (currentTokens + systemTokens <= availableTokens) {
        currentTokens += systemTokens
      } else {
        optimizedContext.systemPrompt = this.truncateText(
          context.systemPrompt,
          availableTokens - currentTokens
        )
        currentTokens = availableTokens
      }
    }

    // Optimize file context
    if (context.fileContext && context.fileContext.length > 0) {
      const { optimizedFiles, tokensUsed } = this.optimizeFileContext(
        context.fileContext,
        availableTokens - currentTokens
      )
      optimizedContext.fileContext = optimizedFiles
      currentTokens += tokensUsed
    }

    // Optimize code context
    if (context.codeContext && context.codeContext.length > 0) {
      const { optimizedCode, tokensUsed } = this.optimizeCodeContext(
        context.codeContext,
        availableTokens - currentTokens
      )
      optimizedContext.codeContext = optimizedCode
      currentTokens += tokensUsed
    }

    // Optimize conversation history
    if (context.conversationHistory && context.conversationHistory.length > 0) {
      const { optimizedHistory, tokensUsed } = this.optimizeConversationHistory(
        context.conversationHistory,
        availableTokens - currentTokens
      )
      optimizedContext.conversationHistory = optimizedHistory
      currentTokens += tokensUsed
    }

    return optimizedContext
  }

  private optimizeFileContext (
    fileContext: FileContext[],
    availableTokens: number
  ): { optimizedFiles: FileContext[]; tokensUsed: number } {
    // Sort by relevance score (higher first)
    const sortedFiles = [...fileContext].sort((a, b) =>
      (b.relevanceScore || 0) - (a.relevanceScore || 0)
    )

    const optimizedFiles: FileContext[] = []
    let tokensUsed = 0

    for (const file of sortedFiles) {
      const fileTokens = this.estimateFileTokens(file)

      if (tokensUsed + fileTokens <= availableTokens) {
        optimizedFiles.push(file)
        tokensUsed += fileTokens
      } else {
        // Try to include a truncated version
        const remainingTokens = availableTokens - tokensUsed
        if (remainingTokens > 100) { // Only if we have significant space left
          const truncatedFile = this.truncateFile(file, remainingTokens)
          optimizedFiles.push(truncatedFile)
          tokensUsed += this.estimateFileTokens(truncatedFile)
        }
        break
      }
    }

    return { optimizedFiles, tokensUsed }
  }

  private optimizeCodeContext (
    codeContext: CodeContext[],
    availableTokens: number
  ): { optimizedCode: CodeContext[]; tokensUsed: number } {
    // Sort by relevance score (higher first)
    const sortedCode = [...codeContext].sort((a, b) =>
      (b.relevanceScore || 0) - (a.relevanceScore || 0)
    )

    const optimizedCode: CodeContext[] = []
    let tokensUsed = 0

    for (const code of sortedCode) {
      const codeTokens = this.estimateCodeTokens(code)

      if (tokensUsed + codeTokens <= availableTokens) {
        optimizedCode.push(code)
        tokensUsed += codeTokens
      } else {
        // Try to include a truncated version
        const remainingTokens = availableTokens - tokensUsed
        if (remainingTokens > 50) { // Only if we have some space left
          const truncatedCode = this.truncateCode(code, remainingTokens)
          optimizedCode.push(truncatedCode)
          tokensUsed += this.estimateCodeTokens(truncatedCode)
        }
        break
      }
    }

    return { optimizedCode, tokensUsed }
  }

  private optimizeConversationHistory (
    history: any[],
    availableTokens: number
  ): { optimizedHistory: any[]; tokensUsed: number } {
    // Keep the most recent messages first
    const reversedHistory = [...history].reverse()
    const optimizedHistory: any[] = []
    let tokensUsed = 0

    for (const message of reversedHistory) {
      const messageTokens = this.estimateTokens(message.content)

      if (tokensUsed + messageTokens <= availableTokens) {
        optimizedHistory.unshift(message) // Add to beginning to maintain order
        tokensUsed += messageTokens
      } else {
        break
      }
    }

    return { optimizedHistory, tokensUsed }
  }

  private estimateFileTokens (file: FileContext): number {
    const pathTokens = this.estimateTokens(file.path)
    const contentTokens = this.estimateTokens(file.content)
    const metadataTokens = 20 // Rough estimate for metadata

    return pathTokens + contentTokens + metadataTokens
  }

  private estimateCodeTokens (code: CodeContext): number {
    const nameTokens = this.estimateTokens(code.name)
    const contentTokens = this.estimateTokens(code.content)
    const locationTokens = this.estimateTokens(code.location.file)
    const metadataTokens = 15 // Rough estimate for metadata

    return nameTokens + contentTokens + locationTokens + metadataTokens
  }

  private truncateFile (file: FileContext, maxTokens: number): FileContext {
    const metadataTokens = 50 // Reserve for path and metadata
    const availableTokens = maxTokens - metadataTokens

    if (availableTokens <= 0) {
      return {
        ...file,
        content: '// Content truncated due to token limits'
      }
    }

    const truncatedContent = this.truncateText(file.content, availableTokens)

    return {
      ...file,
      content: truncatedContent + '\n// ... (content truncated)'
    }
  }

  private truncateCode (code: CodeContext, maxTokens: number): CodeContext {
    const metadataTokens = 30 // Reserve for name and location
    const availableTokens = maxTokens - metadataTokens

    if (availableTokens <= 0) {
      return {
        ...code,
        content: '// Content truncated due to token limits'
      }
    }

    const truncatedContent = this.truncateText(code.content, availableTokens)

    return {
      ...code,
      content: truncatedContent + '\n// ... (truncated)'
    }
  }

  private truncateText (text: string, maxTokens: number): string {
    const maxChars = maxTokens * this.CHARS_PER_TOKEN

    if (text.length <= maxChars) {
      return text
    }

    // Try to truncate at a sensible boundary (line ending)
    const truncated = text.substring(0, maxChars)
    const lastNewline = truncated.lastIndexOf('\n')

    if (lastNewline > maxChars * 0.8) {
      return truncated.substring(0, lastNewline)
    }

    return truncated
  }

  calculateCompressionRatio (original: string, compressed: string): number {
    if (original.length === 0) {return 1}
    return compressed.length / original.length
  }

  getOptimizationReport (
    originalContext: AIContext,
    optimizedContext: AIContext
  ): TokenOptimizationResult {
    const originalPrompt = this.buildContextString(originalContext)
    const optimizedPrompt = this.buildContextString(optimizedContext)

    const removedContext: string[] = []

    // Check what was removed
    if (originalContext.fileContext && optimizedContext.fileContext) {
      const removedFiles = originalContext.fileContext.length - optimizedContext.fileContext.length
      if (removedFiles > 0) {
        removedContext.push(`${removedFiles} file(s)`)
      }
    }

    if (originalContext.codeContext && optimizedContext.codeContext) {
      const removedCode = originalContext.codeContext.length - optimizedContext.codeContext.length
      if (removedCode > 0) {
        removedContext.push(`${removedCode} code context(s)`)
      }
    }

    if (originalContext.conversationHistory && optimizedContext.conversationHistory) {
      const removedHistory = originalContext.conversationHistory.length - optimizedContext.conversationHistory.length
      if (removedHistory > 0) {
        removedContext.push(`${removedHistory} conversation message(s)`)
      }
    }

    return {
      optimizedPrompt,
      removedContext,
      estimatedTokens: this.estimateTokens(optimizedPrompt),
      compressionRatio: this.calculateCompressionRatio(originalPrompt, optimizedPrompt)
    }
  }

  private buildContextString (context: AIContext): string {
    let result = ''

    if (context.systemPrompt) {
      result += context.systemPrompt + '\n'
    }

    if (context.fileContext) {
      result += context.fileContext.map(f => f.content).join('\n') + '\n'
    }

    if (context.codeContext) {
      result += context.codeContext.map(c => c.content).join('\n') + '\n'
    }

    if (context.conversationHistory) {
      result += context.conversationHistory.map(h => h.content).join('\n') + '\n'
    }

    return result
  }
}
