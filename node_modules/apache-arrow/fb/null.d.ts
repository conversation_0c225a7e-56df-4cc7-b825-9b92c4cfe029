import * as flatbuffers from 'flatbuffers';
/**
 * These are stored in the flatbuffer in the Type union below
 */
export declare class Null {
    bb: flatbuffers.ByteBuffer | null;
    bb_pos: number;
    __init(i: number, bb: flatbuffers.ByteBuffer): Null;
    static getRootAsNull(bb: flatbuffers.ByteBuffer, obj?: Null): Null;
    static getSizePrefixedRootAsNull(bb: flatbuffers.ByteBuffer, obj?: Null): Null;
    static startNull(builder: flatbuffers.Builder): void;
    static endNull(builder: flatbuffers.Builder): flatbuffers.Offset;
    static createNull(builder: flatbuffers.Builder): flatbuffers.Offset;
}
