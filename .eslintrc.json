{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/restrict-template-expressions": "off", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/prefer-readonly": "warn", "@typescript-eslint/strict-boolean-expressions": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/member-delimiter-style": "off", "@typescript-eslint/method-signature-style": "off", "@typescript-eslint/quotes": ["error", "single"], "@typescript-eslint/semi": ["error", "never"], "@typescript-eslint/indent": ["error", 2], "@typescript-eslint/space-before-function-paren": ["error", "always"], "no-trailing-spaces": "error", "eol-last": "error", "padded-blocks": "off"}, "ignorePatterns": ["out/**/*", "node_modules/**/*", "*.js"]}