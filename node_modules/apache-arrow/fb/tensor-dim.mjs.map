{"version": 3, "sources": ["fb/tensor-dim.ts"], "names": [], "mappings": "AAAA,qEAAqE;AAErE,OAAO,KAAK,WAAW,MAAM,aAAa,CAAC;AAE3C;;;;GAIG;AACH,MAAM,OAAO,SAAS;IAAtB;QACE,OAAE,GAAgC,IAAI,CAAC;QACvC,WAAM,GAAG,CAAC,CAAC;IAyDb,CAAC;IAxDC,MAAM,CAAC,CAAQ,EAAE,EAAyB;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,EAAyB,EAAE,GAAc;QACjE,OAAO,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED,MAAM,CAAC,8BAA8B,CAAC,EAAyB,EAAE,GAAc;QAC7E,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACH,IAAI;QACF,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACzE,CAAC;IAOD,IAAI,CAAC,gBAAqB;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACnF,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,OAA2B;QAC/C,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,OAA2B,EAAE,IAAW;QACrD,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,OAA2B,EAAE,UAA6B;QACvE,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAA2B;QAC7C,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,OAA2B,EAAE,IAAW,EAAE,UAA6B;QAC5F,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAClC,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACjC,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QACvC,OAAO,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;CACA", "file": "tensor-dim.js", "sourceRoot": "../src"}