"use strict";
// Copyright 2024 Lance Developers.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmbeddingFunction = void 0;
require("reflect-metadata");
const arrow_1 = require("../arrow");
const sanitize_1 = require("../sanitize");
/**
 * An embedding function that automatically creates vector representation for a given column.
 */
class EmbeddingFunction {
    /**
     * sourceField is used in combination with `LanceSchema` to provide a declarative data model
     *
     * @param optionsOrDatatype - The options for the field or the datatype
     *
     * @see {@link lancedb.LanceSchema}
     */
    sourceField(optionsOrDatatype) {
        let datatype = (0, arrow_1.isDataType)(optionsOrDatatype)
            ? optionsOrDatatype
            : optionsOrDatatype?.datatype;
        if (!datatype) {
            throw new Error("Datatype is required");
        }
        datatype = (0, sanitize_1.sanitizeType)(datatype);
        const metadata = new Map();
        metadata.set("source_column_for", this);
        return [datatype, metadata];
    }
    /**
     * vectorField is used in combination with `LanceSchema` to provide a declarative data model
     *
     * @param options - The options for the field
     *
     * @see {@link lancedb.LanceSchema}
     */
    vectorField(optionsOrDatatype) {
        let dtype;
        let vectorType;
        let dims = this.ndims();
        // `func.vectorField(new Float32())`
        if ((0, arrow_1.isDataType)(optionsOrDatatype)) {
            dtype = optionsOrDatatype;
        }
        else {
            // `func.vectorField({
            //  datatype: new Float32(),
            //  dims: 10
            // })`
            dims = dims ?? optionsOrDatatype?.dims;
            dtype = optionsOrDatatype?.datatype;
        }
        if (dtype !== undefined) {
            // `func.vectorField(new FixedSizeList(dims, new Field("item", new Float32(), true)))`
            // or `func.vectorField({datatype: new FixedSizeList(dims, new Field("item", new Float32(), true))})`
            if ((0, arrow_1.isFixedSizeList)(dtype)) {
                vectorType = dtype;
                // `func.vectorField(new Float32())`
                // or `func.vectorField({datatype: new Float32()})`
            }
            else if ((0, arrow_1.isFloat)(dtype)) {
                // No `ndims` impl and no `{dims: n}` provided;
                if (dims === undefined) {
                    throw new Error("ndims is required for vector field");
                }
                vectorType = (0, arrow_1.newVectorType)(dims, dtype);
            }
            else {
                throw new Error("Expected FixedSizeList or Float as datatype for vector field");
            }
        }
        else {
            if (dims === undefined) {
                throw new Error("ndims is required for vector field");
            }
            vectorType = new arrow_1.FixedSizeList(dims, new arrow_1.Field("item", new arrow_1.Float32(), true));
        }
        const metadata = new Map();
        metadata.set("vector_column_for", this);
        return [vectorType, metadata];
    }
    /** The number of dimensions of the embeddings */
    ndims() {
        return undefined;
    }
    /**
    Compute the embeddings for a single query
   */
    async computeQueryEmbeddings(data) {
        return this.computeSourceEmbeddings([data]).then((embeddings) => embeddings[0]);
    }
}
exports.EmbeddingFunction = EmbeddingFunction;
