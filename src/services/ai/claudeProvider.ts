import Anthropic from '@anthropic-ai/sdk'
import { AIProvider, AIContext, AIResponse, AIConfig } from '../../types/aiTypes'
import { Logger } from '../../utils/logger'
import { StorageService } from '../storageService'

export class Claude<PERSON>rovider implements AIProvider {
  public readonly name = 'claude'
  private client: Anthropic | null = null
  private config: AIConfig | null = null

  constructor (
    private readonly logger: Logger,
    private readonly storageService: StorageService
  ) {}

  async initialize (config: AIConfig): Promise<void> {
    this.config = config

    const claudeApiKey = await this.storageService.getSecret('claudeApiKey')
    if (!claude<PERSON>pi<PERSON>ey) {
      throw new Error('Claude API key not configured')
    }

    this.client = new Anthropic({
      apiKey: claude<PERSON>pi<PERSON>ey,
      timeout: config.timeout || 30000
    })

    this.logger.info('Claude provider initialized')
  }

  async generateResponse (prompt: string, context?: AIContext): Promise<AIResponse> {
    if (!this.client || !this.config) {
      throw new Error('Claude provider not initialized')
    }

    try {
      const { systemPrompt, messages } = this.buildMessages(prompt, context)
      const model = this.getClaudeModel(context?.model || this.config.model)
      const maxTokens = context?.maxTokens || this.config.maxTokens
      const temperature = context?.temperature || this.config.temperature

      this.logger.debug(`Sending request to Claude with model: ${model}`)

      const response = await this.client.messages.create({
        model: model,
        system: systemPrompt,
        messages: messages,
        max_tokens: maxTokens,
        temperature: temperature
      })

      const content = response.content[0]
      if (!content || content.type !== 'text') {
        throw new Error('No valid response generated from Claude')
      }

      const result: AIResponse = {
        content: content.text,
        tokensUsed: response.usage?.input_tokens + response.usage?.output_tokens,
        model: response.model,
        finishReason: response.stop_reason || undefined,
        usage: response.usage ? {
          promptTokens: response.usage.input_tokens,
          completionTokens: response.usage.output_tokens,
          totalTokens: response.usage.input_tokens + response.usage.output_tokens
        } : undefined
      }

      this.logger.debug(`Claude response received: ${result.tokensUsed} tokens used`)
      return result

    } catch (error) {
      this.logger.error('Claude API error:', error)
      throw this.handleClaudeError(error)
    }
  }

  async validateConfig (): Promise<boolean> {
    if (!this.client || !this.config) {
      return false
    }

    try {
      const claudeApiKey = await this.storageService.getSecret('claudeApiKey')
      if (!claudeApiKey) {
        return false
      }

      // Test with a simple request
      const response = await this.client.messages.create({
        model: this.getClaudeModel(this.config.model),
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5
      })

      return response.content.length > 0
    } catch (error) {
      this.logger.error('Claude configuration validation failed:', error)
      return false
    }
  }

  estimateTokens (text: string): number {
    // Claude tokens are roughly similar to OpenAI
    // This is a simplified estimation
    return Math.ceil(text.length / 4)
  }

  private buildMessages (prompt: string, context?: AIContext): {
    systemPrompt: string;
    messages: Anthropic.Messages.MessageParam[]
  } {
    const messages: Anthropic.Messages.MessageParam[] = []
    let systemPrompt = ''

    // Build system prompt
    if (context?.systemPrompt) {
      systemPrompt += context.systemPrompt
    }

    // Add file context to system prompt if provided
    if (context?.fileContext && context.fileContext.length > 0) {
      const fileContextContent = this.buildFileContext(context.fileContext)
      systemPrompt += `\n\nHere is the relevant file context:\n\n${fileContextContent}`
    }

    // Add code context to system prompt if provided
    if (context?.codeContext && context.codeContext.length > 0) {
      const codeContextContent = this.buildCodeContext(context.codeContext)
      systemPrompt += `\n\nHere is the relevant code context:\n\n${codeContextContent}`
    }

    // Add conversation history if provided
    if (context?.conversationHistory) {
      for (const msg of context.conversationHistory) {
        if (msg.role !== 'system') { // Claude handles system messages differently
          messages.push({
            role: msg.role === 'assistant' ? 'assistant' : 'user',
            content: msg.content
          })
        }
      }
    }

    // Add the main prompt
    messages.push({
      role: 'user',
      content: prompt
    })

    return { systemPrompt, messages }
  }

  private buildFileContext (fileContext: any[]): string {
    return fileContext
      .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
      .map(file => `File: ${file.path}\nLanguage: ${file.language || 'unknown'}\nContent:\n${file.content}`)
      .join('\n\n---\n\n')
  }

  private buildCodeContext (codeContext: any[]): string {
    return codeContext
      .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
      .map(code => `${code.type}: ${code.name}\nLocation: ${code.location.file}:${code.location.line}\nContent:\n${code.content}`)
      .join('\n\n---\n\n')
  }

  private getClaudeModel (model: string): string {
    // Map generic model names to Claude-specific models
    const modelMapping: { [key: string]: string } = {
      'claude-3-opus': 'claude-3-opus-20240229',
      'claude-3-sonnet': 'claude-3-sonnet-20240229',
      'claude-3-haiku': 'claude-3-haiku-20240307',
      'gpt-4': 'claude-3-sonnet-20240229', // Default fallback
      'gpt-4-turbo': 'claude-3-opus-20240229'
    }

    return modelMapping[model] || 'claude-3-sonnet-20240229'
  }

  private handleClaudeError (error: any): Error {
    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      switch (status) {
        case 401:
          return new Error('Invalid Claude API key')
        case 429:
          return new Error('Claude API rate limit exceeded')
        case 500:
          return new Error('Claude API server error')
        default:
          return new Error(`Claude API error: ${data?.error?.message || 'Unknown error'}`)
      }
    }

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return new Error('Cannot connect to Claude API')
    }

    return new Error(`Claude provider error: ${error.message}`)
  }

  async dispose (): Promise<void> {
    this.client = null
    this.config = null
    this.logger.info('Claude provider disposed')
  }
}
