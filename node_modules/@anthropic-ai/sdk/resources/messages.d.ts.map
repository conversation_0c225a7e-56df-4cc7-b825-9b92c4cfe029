{"version": 3, "file": "messages.d.ts", "sourceRoot": "", "sources": ["../src/resources/messages.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAC/C,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,MAAM,qCAAqC,CAAC;AACpE,OAAO,EAAE,aAAa,EAAE,MAAM,qCAAqC,CAAC;AACpE,OAAO,KAAK,WAAW,MAAM,sCAAsC,CAAC;AACpE,OAAO,EAAE,MAAM,EAAE,MAAM,6BAA6B,CAAC;AAErD,qBAAa,QAAS,SAAQ,WAAW;IACvC;;;;;;;;OAQG;IACH,MAAM,CAAC,IAAI,EAAE,+BAA+B,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC;IACjG,MAAM,CACJ,IAAI,EAAE,4BAA4B,EAClC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,UAAU,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;IACzC,MAAM,CACJ,IAAI,EAAE,uBAAuB,EAC7B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,UAAU,CAAC,MAAM,CAAC,kBAAkB,CAAC,GAAG,OAAO,CAAC;IAanD;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,aAAa;CAGhF;AAED,MAAM,WAAW,YAAY;IAC3B,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,sBAAsB;IACrC,KAAK,EAAE,SAAS,CAAC;IAEjB,KAAK,EAAE,MAAM,CAAC;IAEd,IAAI,EAAE,qBAAqB,CAAC;CAC7B;AAED,MAAM,WAAW,sBAAsB;IACrC,aAAa,EAAE,YAAY,CAAC;IAE5B,KAAK,EAAE,MAAM,CAAC;IAEd,IAAI,EAAE,qBAAqB,CAAC;CAC7B;AAED,MAAM,WAAW,qBAAqB;IACpC,KAAK,EAAE,MAAM,CAAC;IAEd,IAAI,EAAE,oBAAoB,CAAC;CAC5B;AAED,MAAM,WAAW,eAAe;IAC9B,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC;IAE/B,IAAI,CAAC,EAAE,OAAO,CAAC;CAChB;AAED,yBAAiB,eAAe,CAAC;IAC/B,UAAiB,MAAM;QACrB,IAAI,EAAE,MAAM,CAAC;QAEb,UAAU,EAAE,YAAY,GAAG,WAAW,GAAG,WAAW,GAAG,YAAY,CAAC;QAEpE,IAAI,CAAC,EAAE,QAAQ,CAAC;KACjB;CACF;AAED,MAAM,WAAW,OAAO;IACtB;;;;OAIG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;IAE7B;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;;;OAIG;IACH,IAAI,EAAE,WAAW,CAAC;IAElB;;;;;;;;;;;;;;OAcG;IACH,WAAW,EAAE,UAAU,GAAG,YAAY,GAAG,eAAe,GAAG,IAAI,CAAC;IAEhE;;;;;OAKG;IACH,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IAE7B;;;;OAIG;IACH,IAAI,EAAE,SAAS,CAAC;IAEhB;;;;;;;;;;;;;OAaG;IACH,KAAK,EAAE,KAAK,CAAC;CACd;AAED,MAAM,WAAW,iBAAiB;IAChC,KAAK,EAAE,iBAAiB,CAAC,KAAK,CAAC;IAE/B,IAAI,EAAE,eAAe,CAAC;IAEtB;;;;;;;;;;;;;OAaG;IACH,KAAK,EAAE,iBAAiB,CAAC;CAC1B;AAED,yBAAiB,iBAAiB,CAAC;IACjC,UAAiB,KAAK;QACpB,WAAW,EAAE,UAAU,GAAG,YAAY,GAAG,eAAe,GAAG,IAAI,CAAC;QAEhE,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;KAC9B;CACF;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,YAAY;IAC3B,OAAO,EAAE,MAAM,GAAG,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,CAAC;IAErD,IAAI,EAAE,MAAM,GAAG,WAAW,CAAC;CAC5B;AAED,MAAM,WAAW,iBAAiB;IAChC,OAAO,EAAE,OAAO,CAAC;IAEjB,IAAI,EAAE,eAAe,CAAC;CACvB;AAED,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,cAAc,CAAC;CACtB;AAED,MAAM,MAAM,kBAAkB,GAC1B,iBAAiB,GACjB,iBAAiB,GACjB,gBAAgB,GAChB,sBAAsB,GACtB,sBAAsB,GACtB,qBAAqB,CAAC;AAE1B,MAAM,WAAW,SAAS;IACxB,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,SAAS;IACxB,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,YAAY,CAAC;CACpB;AAED,MAAM,WAAW,KAAK;IACpB;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,MAAM,mBAAmB,GAAG,+BAA+B,GAAG,4BAA4B,CAAC;AAEjG,MAAM,WAAW,uBAAuB;IACtC;;;;;;;;OAQG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsFG;IACH,QAAQ,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;IAE9B;;;;;OAKG;IACH,KAAK,EACD,CAAC,MAAM,GAAG,EAAE,CAAC,GACb,wBAAwB,GACxB,0BAA0B,GAC1B,aAAa,GACb,YAAY,GACZ,oBAAoB,CAAC;IAEzB;;OAEG;IACH,QAAQ,CAAC,EAAE,mBAAmB,CAAC,QAAQ,CAAC;IAExC;;;;;;;;;;OAUG;IACH,cAAc,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE/B;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAEhB;;;;;;;;;OASG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;;;;;;OAQG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;;;;;;;;;OAUG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,yBAAiB,mBAAmB,CAAC;IACnC;;OAEG;IACH,UAAiB,QAAQ;QACvB;;;;;;WAMG;QACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED,KAAY,+BAA+B,GAAG,WAAW,CAAC,+BAA+B,CAAC;IAC1F,KAAY,4BAA4B,GAAG,WAAW,CAAC,4BAA4B,CAAC;CACrF;AAED,MAAM,WAAW,+BAAgC,SAAQ,uBAAuB;IAC9E;;;;;OAKG;IACH,MAAM,CAAC,EAAE,KAAK,CAAC;CAChB;AAED,MAAM,WAAW,4BAA6B,SAAQ,uBAAuB;IAC3E;;;;;OAKG;IACH,MAAM,EAAE,IAAI,CAAC;CACd;AAED,MAAM,WAAW,mBAAmB;IAClC;;;;;;;;OAQG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsFG;IACH,QAAQ,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;IAE9B;;;;;OAKG;IACH,KAAK,EACD,CAAC,MAAM,GAAG,EAAE,CAAC,GACb,wBAAwB,GACxB,0BAA0B,GAC1B,aAAa,GACb,YAAY,GACZ,oBAAoB,CAAC;IAEzB;;OAEG;IACH,QAAQ,CAAC,EAAE,mBAAmB,CAAC,QAAQ,CAAC;IAExC;;;;;;;;;;OAUG;IACH,cAAc,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE/B;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAEhB;;;;;;;;;OASG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;;;;;;OAQG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;;;;;;;;;OAUG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,yBAAiB,mBAAmB,CAAC;IACnC;;OAEG;IACH,UAAiB,QAAQ;QACvB;;;;;;WAMG;QACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;CACF;AAED,yBAAiB,QAAQ,CAAC;IACxB,MAAM,QAAQ,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;IACtD,MAAM,QAAQ,sBAAsB,GAAG,WAAW,CAAC,sBAAsB,CAAC;IAC1E,MAAM,QAAQ,sBAAsB,GAAG,WAAW,CAAC,sBAAsB,CAAC;IAC1E,MAAM,QAAQ,qBAAqB,GAAG,WAAW,CAAC,qBAAqB,CAAC;IACxE,MAAM,QAAQ,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;IAC5D,MAAM,QAAQ,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;IAC5C,MAAM,QAAQ,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;IAChE,MAAM,QAAQ,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;IAChE,MAAM,QAAQ,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;IACtD,MAAM,QAAQ,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;IAChE,MAAM,QAAQ,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC;IAC9D,MAAM,QAAQ,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;IAClE,MAAM,QAAQ,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;IAChD,MAAM,QAAQ,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;IAChD,MAAM,QAAQ,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;IACxC,MAAM,QAAQ,mBAAmB,GAAG,WAAW,CAAC,mBAAmB,CAAC;IACpE,MAAM,QAAQ,+BAA+B,GAAG,WAAW,CAAC,+BAA+B,CAAC;IAC5F,MAAM,QAAQ,4BAA4B,GAAG,WAAW,CAAC,4BAA4B,CAAC;IACtF,MAAM,QAAQ,mBAAmB,GAAG,WAAW,CAAC,mBAAmB,CAAC;CACrE"}