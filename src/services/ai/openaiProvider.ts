import OpenAI from 'openai'
import { <PERSON><PERSON>rovider, AIContext, AIResponse, AIConfig } from '../../types/aiTypes'
import { Logger } from '../../utils/logger'
import { StorageService } from '../storageService'

export class OpenAIProvider implements AIProvider {
  public readonly name = 'openai'
  private client: OpenAI | null = null
  private config: AIConfig | null = null

  constructor (
    private readonly logger: Logger,
    private readonly storageService: StorageService
  ) {}

  async initialize (config: AIConfig): Promise<void> {
    this.config = config

    if (!config.apiKey) {
      throw new Error('OpenAI API key not configured')
    }

    this.client = new OpenAI({
      apiKey: config.apiKey,
      timeout: config.timeout || 30000
    })

    this.logger.info('OpenAI provider initialized')
  }

  async generateResponse (prompt: string, context?: AIContext): Promise<AIResponse> {
    if (!this.client || !this.config) {
      throw new Error('OpenAI provider not initialized')
    }

    try {
      const messages = this.buildMessages(prompt, context)
      const model = context?.model || this.config.model
      const maxTokens = context?.maxTokens || this.config.maxTokens
      const temperature = context?.temperature || this.config.temperature

      this.logger.debug(`Sending request to OpenAI with model: ${model}`)

      const response = await this.client.chat.completions.create({
        model: model,
        messages: messages,
        max_tokens: maxTokens,
        temperature: temperature,
        stream: false
      })

      const choice = response.choices[0]
      if (!choice) {
        throw new Error('No response generated from OpenAI')
      }

      const result: AIResponse = {
        content: choice.message.content || '',
        tokensUsed: response.usage?.total_tokens,
        model: response.model,
        finishReason: choice.finish_reason || undefined,
        usage: response.usage ? {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens
        } : undefined
      }

      this.logger.debug(`OpenAI response received: ${result.tokensUsed} tokens used`)
      return result

    } catch (error) {
      this.logger.error('OpenAI API error:', error)
      throw this.handleOpenAIError(error)
    }
  }

  async validateConfig (): Promise<boolean> {
    if (!this.client || !this.config) {
      return false
    }

    try {
      // Test with a simple request
      const response = await this.client.chat.completions.create({
        model: this.config.model,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5
      })

      return response.choices.length > 0
    } catch (error) {
      this.logger.error('OpenAI configuration validation failed:', error)
      return false
    }
  }

  estimateTokens (text: string): number {
    // Rough estimation: ~4 characters per token for English text
    // This is a simplified estimation, real tokenization is more complex
    return Math.ceil(text.length / 4)
  }

  private buildMessages (prompt: string, context?: AIContext): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = []

    // Add system prompt if provided
    if (context?.systemPrompt) {
      messages.push({
        role: 'system',
        content: context.systemPrompt
      })
    }

    // Add conversation history if provided
    if (context?.conversationHistory) {
      for (const msg of context.conversationHistory) {
        messages.push({
          role: msg.role,
          content: msg.content
        })
      }
    }

    // Add file context if provided
    if (context?.fileContext && context.fileContext.length > 0) {
      const fileContextContent = this.buildFileContext(context.fileContext)
      messages.push({
        role: 'system',
        content: `Here is the relevant file context:\n\n${fileContextContent}`
      })
    }

    // Add code context if provided
    if (context?.codeContext && context.codeContext.length > 0) {
      const codeContextContent = this.buildCodeContext(context.codeContext)
      messages.push({
        role: 'system',
        content: `Here is the relevant code context:\n\n${codeContextContent}`
      })
    }

    // Add the main prompt
    messages.push({
      role: 'user',
      content: prompt
    })

    return messages
  }

  private buildFileContext (fileContext: any[]): string {
    return fileContext
      .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
      .map(file => `File: ${file.path}\nLanguage: ${file.language || 'unknown'}\nContent:\n${file.content}`)
      .join('\n\n---\n\n')
  }

  private buildCodeContext (codeContext: any[]): string {
    return codeContext
      .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
      .map(code => `${code.type}: ${code.name}\nLocation: ${code.location.file}:${code.location.line}\nContent:\n${code.content}`)
      .join('\n\n---\n\n')
  }

  private handleOpenAIError (error: any): Error {
    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      switch (status) {
        case 401:
          return new Error('Invalid OpenAI API key')
        case 429:
          return new Error('OpenAI API rate limit exceeded')
        case 500:
          return new Error('OpenAI API server error')
        default:
          return new Error(`OpenAI API error: ${data?.error?.message || 'Unknown error'}`)
      }
    }

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return new Error('Cannot connect to OpenAI API')
    }

    return new Error(`OpenAI provider error: ${error.message}`)
  }

  async dispose (): Promise<void> {
    this.client = null
    this.config = null
    this.logger.info('OpenAI provider disposed')
  }
}
