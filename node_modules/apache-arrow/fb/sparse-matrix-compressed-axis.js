"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.SparseMatrixCompressedAxis = void 0;
var SparseMatrixCompressedAxis;
(function (SparseMatrixCompressedAxis) {
    SparseMatrixCompressedAxis[SparseMatrixCompressedAxis["Row"] = 0] = "Row";
    SparseMatrixCompressedAxis[SparseMatrixCompressedAxis["Column"] = 1] = "Column";
})(SparseMatrixCompressedAxis || (exports.SparseMatrixCompressedAxis = SparseMatrixCompressedAxis = {}));

//# sourceMappingURL=sparse-matrix-compressed-axis.js.map
