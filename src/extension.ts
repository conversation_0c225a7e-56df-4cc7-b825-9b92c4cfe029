import * as vscode from 'vscode'
import { TransformCommand } from './commands/transformCommand'
import { SuggestCommand } from './commands/suggestCommand'
import { ConfigureCommand } from './commands/configureCommand'
import { StorageService } from './services/storageService'
import { Logger } from './utils/logger'

let logger: Logger
let storageService: StorageService

export function activate (context: vscode.ExtensionContext) {
  // Initialize core services
  logger = new Logger('TaskTransformer')
  storageService = new StorageService(context)

  logger.info('Task Transformer extension is activating...')

  // Register commands
  const transformCommand = new TransformCommand(context, logger, storageService)
  const suggestCommand = new SuggestCommand(context, logger, storageService)
  const configureCommand = new ConfigureCommand(context, logger, storageService)

  // Register command handlers
  context.subscriptions.push(
    vscode.commands.registerCommand('taskTransformer.transform',
      (...args) => transformCommand.execute(...args)
    ),
    vscode.commands.registerCommand('taskTransformer.suggest',
      (...args) => suggestCommand.execute(...args)
    ),
    vscode.commands.registerCommand('taskTransformer.configure',
      (...args) => configureCommand.execute(...args)
    )
  )

  // Create status bar item
  const statusBarItem = vscode.window.createStatusBarItem(
    vscode.StatusBarAlignment.Right,
    100
  )
  statusBarItem.text = '$(brain) Task Transformer'
  statusBarItem.tooltip = 'Transform natural language to structured tasks'
  statusBarItem.command = 'taskTransformer.transform'
  statusBarItem.show()
  context.subscriptions.push(statusBarItem)

  // Initialize background services
  initializeBackgroundServices(context)

  logger.info('Task Transformer extension activated successfully')
}

async function initializeBackgroundServices (_context: vscode.ExtensionContext) {
  try {
    // Initialize indexing service if enabled
    const config = vscode.workspace.getConfiguration('taskTransformer')
    const indexingEnabled = config.get<boolean>('indexingEnabled', true)

    if (indexingEnabled) {
      logger.info('Initializing indexing service...')
      // Note: IndexingService will be implemented in later tasks
    }

    // Initialize AI service
    logger.info('Initializing AI service...')
    // Note: AI service will be implemented in later tasks

  } catch (error) {
    logger.error('Failed to initialize background services:', error)
  }
}

export function deactivate () {
  logger?.info('Task Transformer extension is deactivating...')
  // Cleanup resources
  storageService?.dispose()
  logger?.dispose()
}
