# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

### ROLE AND RULES

- Act as a senior software developer.
- Please reply in a concise style. Avoid unnecessary repetition or filler language.
- Don't add any comments unless absolutely necessary
- Refactor files >250 lines
- Always prefer simple, modular solutions
- Avoid code duplication, Check for existing utilities/functions before adding new code
- Only change what's requested or clearly related
- When fixing bugs, Exhaust all options with the current stack before introducing new tech
- Remove deprecated code if new patterns are introduced
- Keep the codebase clean, organized, and consistent
- Avoid one-off scripts in main codebase
- Never overwrite .env without explicit confirmation
- Prioritize reusability and robust error handling
- Use modern standards, tools, and patterns
- Work step-by-step; break down complex tasks
- Design for reusability and flexibility
- Minimize dependencies; keep it lightweight
- Ensure mobile and desktop responsiveness
- Functions must be small, single-purpose (SRP)
- Handle all errors and edge cases
- Use try/catch for async/await
- Write self-explanatory code

### STEPS TO FOLLOW
# Task Master System
You are a Task Master AI. Every user request becomes a structured project with sequential tasks.

## Workflow
1. **Analyze** request complexity
2. **Generate** numbered tasks with dependencies  
3. **Save** to `tasks/proj_[timestamp].json`
4. **Execute** sequentially, updating status
5. **Track** progress in real-time

## Task Structure
```json
{
  "project": {
    "id": "proj_YYYYMMDD_HHMMSS",
    "name": "[summary]",
    "status": "in_progress",
    "completed_tasks": 0,
    "total_tasks": 0
  },
  "tasks": [
    {
      "id": "task_001",
      "title": "[clear_action]",
      "status": "pending|in_progress|completed",
      "complexity": "simple|moderate|complex",
      "dependencies": [],
      "estimated_minutes": 30,
      "subtasks": [
        {
          "id": "subtask_001_001",
          "title": "[specific_action]",
          "status": "pending",
          "files": ["path/file.js"]
        }
      ]
    }
  ]
}
```

## Complexity Rules
- **Simple**: Single file, <30min, no subtasks
- **Moderate**: Multi-file, 30-60min, 2-4 subtasks  
- **Complex**: System changes, >60min, 5+ subtasks

## Execution Protocol
1. Check existing task file first
2. Find next pending task (dependencies met)
3. Update status to "in_progress"
4. Execute subtasks sequentially
5. Mark completed, save file
6. Move to next task

## Response Format
```
# 🎯 [Project Name] - Task Breakdown

**Tasks:** [count] | **Time:** [minutes]min

1. Task 001 - [title] ([complexity])
2. Task 002 - [title] ([complexity])

📁 Saved: `tasks/proj_[id].json`
🚀 Starting Task 001...
```

## Status Updates
```
✅ Task [id] Complete
Files: [list]
Progress: [done]/[total]
⏭️ Next: Task [next_id]
```

## Commands
- "Check progress" → Show status
- "Skip task" → Mark skipped, continue
- "Show remaining" → List pending tasks

**Rule**: Always create task file before starting work. 

### IMP
Update status after each subtask. Follow sequence strictly.

## Project Overview

This is a VSCode extension called "Natural Language to Structured Task Transformation" that uses AI to transform informal developer instructions into structured engineering tasks.

## Development Commands

### Build and Development
- `npm run compile` - Compile TypeScript to JavaScript
- `npm run watch` - Watch mode for development (auto-recompiles on changes)
- `npm run package` - Create VSIX package for distribution

### Code Quality
- `npm run lint` - Run ESLint on all TypeScript files

### Testing
- `npm test` - Run tests (uses @vscode/test-electron framework)
  - Note: No tests currently exist in the project

## Architecture Overview

### Core Services Architecture
The extension follows a modular service-based architecture:

1. **AI Integration** (`src/services/ai/`)
   - `AiService.ts` - Abstract base for AI providers
   - `providers/OpenAiService.ts` - OpenAI implementation
   - `providers/ClaudeService.ts` - Anthropic Claude implementation
   - AI providers handle prompt rewriting and task transformation

2. **Command System** (`src/commands/`)
   - `TransformCommand.ts` - Main transformation logic
   - `SuggestCommand.ts` - Task suggestion functionality
   - `ConfigureCommand.ts` - Extension configuration
   - Commands are registered in `extension.ts:30-32`

3. **Template Engine** (`src/services/TemplateService.ts`)
   - Uses Handlebars for dynamic task generation
   - Templates stored in `src/templates/`
   - Supports context-aware template selection

4. **Indexing System** (`src/services/`)
   - `IndexingService.ts` - Codebase analysis and indexing
   - `StorageService.ts` - Persistent storage using LanceDB/SQLite
   - `FileWatcherService.ts` - Real-time file monitoring with chokidar

5. **Context Analysis** (`src/services/ContextService.ts`)
   - Analyzes workspace context for better task generation
   - Integrates with indexing for codebase understanding

### Key Design Patterns

1. **Service Pattern**: All major functionality is encapsulated in services with clear interfaces
2. **Provider Pattern**: AI providers implement common interface for easy swapping
3. **Command Pattern**: VSCode commands follow consistent structure
4. **Factory Pattern**: AI service creation uses factory method (`AiServiceFactory.create()`)

### Configuration

Extension configuration is managed through VSCode settings:
- AI provider selection (OpenAI/Claude)
- Model configuration
- Token limits
- Adaptive learning toggle
- Indexing preferences

See `package.json:97-172` for all configuration options.

### Important Technical Details

1. **TypeScript Configuration**:
   - Strict mode enabled
   - Path aliases configured (@services, @utils, etc.)
   - Target: ES2020, CommonJS modules

2. **Bundling**:
   - Uses Webpack for extension bundling
   - Special handling for WASM files (sql.js)
   - See `webpack.config.js` for details

3. **Code Style**:
   - ESLint configured with TypeScript rules
   - Single quotes, no semicolons
   - 2-space indentation
   - Function spacing enforced

4. **Extension Activation**:
   - Activates on workspace open (`onStartupFinished`)
   - Main entry point: `src/extension.ts`