import * as vscode from 'vscode'

export class StorageService {
  constructor (private readonly context: vscode.ExtensionContext) {}

  // Global state management
  async getGlobalValue<T>(key: string, defaultValue?: T): Promise<T | undefined> {
    return this.context.globalState.get(key, defaultValue)
  }

  async setGlobalValue<T>(key: string, value: T): Promise<void> {
    await this.context.globalState.update(key, value)
  }

  // Workspace state management
  async getWorkspaceValue<T>(key: string, defaultValue?: T): Promise<T | undefined> {
    return this.context.workspaceState.get(key, defaultValue)
  }

  async setWorkspaceValue<T>(key: string, value: T): Promise<void> {
    await this.context.workspaceState.update(key, value)
  }

  // Secret storage management
  async storeSecret (key: string, value: string): Promise<void> {
    await this.context.secrets.store(key, value)
  }

  async getSecret (key: string): Promise<string | undefined> {
    return await this.context.secrets.get(key)
  }

  async deleteSecret (key: string): Promise<void> {
    await this.context.secrets.delete(key)
  }

  // User preferences
  async getUserPreferences (): Promise<any> {
    return await this.getGlobalValue('userPreferences', {})
  }

  async setUserPreferences (preferences: any): Promise<void> {
    await this.setGlobalValue('userPreferences', preferences)
  }

  // Project patterns
  async getProjectPatterns (): Promise<any> {
    return await this.getWorkspaceValue('projectPatterns', {})
  }

  async setProjectPatterns (patterns: any): Promise<void> {
    await this.setWorkspaceValue('projectPatterns', patterns)
  }

  // Learning cache
  async getLearningCache (): Promise<any> {
    return await this.getGlobalValue('learningCache', {})
  }

  async setLearningCache (cache: any): Promise<void> {
    await this.setGlobalValue('learningCache', cache)
  }

  // Extension configuration
  getConfiguration (): vscode.WorkspaceConfiguration {
    return vscode.workspace.getConfiguration('taskTransformer')
  }

  // Cleanup
  dispose (): void {
    // Cleanup resources if needed
  }
}
