// automatically generated by the FlatBuffers compiler, do not modify
import * as flatbuffers from 'flatbuffers';
/**
 * Same as List, but with 64-bit offsets, allowing to represent
 * extremely large data values.
 */
export class LargeList {
    constructor() {
        this.bb = null;
        this.bb_pos = 0;
    }
    __init(i, bb) {
        this.bb_pos = i;
        this.bb = bb;
        return this;
    }
    static getRootAsLargeList(bb, obj) {
        return (obj || new LargeList()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    static getSizePrefixedRootAsLargeList(bb, obj) {
        bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
        return (obj || new LargeList()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    static startLargeList(builder) {
        builder.startObject(0);
    }
    static endLargeList(builder) {
        const offset = builder.endObject();
        return offset;
    }
    static createLargeList(builder) {
        LargeList.startLargeList(builder);
        return LargeList.endLargeList(builder);
    }
}

//# sourceMappingURL=large-list.mjs.map
