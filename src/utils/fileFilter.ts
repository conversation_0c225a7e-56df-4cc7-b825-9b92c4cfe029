import * as path from 'path'
import * as fs from 'fs'

export interface FileFilterOptions {
  includePatterns?: string[]
  excludePatterns?: string[]
  ignoreHidden?: boolean
  ignoreNodeModules?: boolean
  ignoreGitFiles?: boolean
  customIgnorePaths?: string[]
  maxFileSize?: number
  allowedExtensions?: string[]
  blockedExtensions?: string[]
}

export class FileFilter {
  private includePatterns: RegExp[] = []
  private excludePatterns: RegExp[] = []
  private options: FileFilterOptions

  constructor(options: FileFilterOptions = {}) {
    this.options = {
      ignoreHidden: true,
      ignoreNodeModules: true,
      ignoreGitFiles: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      ...options
    }

    this.setupPatterns()
  }

  private setupPatterns(): void {
    const defaultExcludePatterns = [
      'node_modules/**',
      '.git/**',
      'dist/**',
      'build/**',
      '*.log',
      '*.tmp',
      '*.temp',
      '.env*',
      '.DS_Store',
      'Thumbs.db'
    ]

    const excludePatterns = [
      ...defaultExcludePatterns,
      ...(this.options.excludePatterns || [])
    ]

    this.excludePatterns = excludePatterns.map(pattern => this.globToRegex(pattern))
    this.includePatterns = (this.options.includePatterns || []).map(pattern => this.globToRegex(pattern))
  }

  private globToRegex(pattern: string): RegExp {
    const escapeRegex = (str: string) => str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    
    let regexPattern = pattern
      .split('/')
      .map(part => {
        if (part === '**') return '.*'
        if (part === '*') return '[^/]*'
        if (part.includes('*')) {
          return part.replace(/\*/g, '[^/]*')
        }
        return escapeRegex(part)
      })
      .join('/')

    if (pattern.endsWith('/**')) {
      regexPattern = regexPattern.replace(/\/\.\*$/, '(/.*)?')
    }

    return new RegExp(`^${regexPattern}$`, 'i')
  }

  shouldInclude(filePath: string): boolean {
    const normalizedPath = path.normalize(filePath).replace(/\\/g, '/')
    
    if (this.options.ignoreHidden && this.isHidden(normalizedPath)) {
      return false
    }

    if (this.options.ignoreNodeModules && this.isNodeModules(normalizedPath)) {
      return false
    }

    if (this.options.ignoreGitFiles && this.isGitFile(normalizedPath)) {
      return false
    }

    for (const pattern of this.excludePatterns) {
      if (pattern.test(normalizedPath)) {
        return false
      }
    }

    if (this.includePatterns.length > 0) {
      return this.includePatterns.some(pattern => pattern.test(normalizedPath))
    }

    return true
  }

  async shouldIncludeFile(filePath: string): Promise<boolean> {
    if (!this.shouldInclude(filePath)) {
      return false
    }

    try {
      const stats = await fs.promises.stat(filePath)
      
      if (!stats.isFile()) {
        return false
      }

      if (this.options.maxFileSize && stats.size > this.options.maxFileSize) {
        return false
      }

      const ext = path.extname(filePath).toLowerCase()
      
      if (this.options.allowedExtensions) {
        return this.options.allowedExtensions.includes(ext)
      }

      if (this.options.blockedExtensions) {
        return !this.options.blockedExtensions.includes(ext)
      }

      return true
    } catch {
      return false
    }
  }

  private isHidden(filePath: string): boolean {
    const basename = path.basename(filePath)
    return basename.startsWith('.') && basename !== '.'
  }

  private isNodeModules(filePath: string): boolean {
    return filePath.includes('/node_modules/') || filePath.includes('\\node_modules\\')
  }

  private isGitFile(filePath: string): boolean {
    return filePath.includes('/.git/') || filePath.includes('\\.git\\')
  }

  static async loadGitignore(rootPath: string): Promise<string[]> {
    const gitignorePath = path.join(rootPath, '.gitignore')
    
    try {
      const content = await fs.promises.readFile(gitignorePath, 'utf-8')
      return content
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0 && !line.startsWith('#'))
    } catch {
      return []
    }
  }

  static async createFromGitignore(rootPath: string, additionalOptions: FileFilterOptions = {}): Promise<FileFilter> {
    const gitignorePatterns = await FileFilter.loadGitignore(rootPath)
    
    return new FileFilter({
      ...additionalOptions,
      excludePatterns: [
        ...(additionalOptions.excludePatterns || []),
        ...gitignorePatterns
      ]
    })
  }

  addExcludePattern(pattern: string): void {
    this.excludePatterns.push(this.globToRegex(pattern))
  }

  addIncludePattern(pattern: string): void {
    this.includePatterns.push(this.globToRegex(pattern))
  }

  clearPatterns(): void {
    this.includePatterns = []
    this.excludePatterns = []
    this.setupPatterns()
  }

  getPatterns(): { include: string[], exclude: string[] } {
    return {
      include: this.options.includePatterns || [],
      exclude: this.options.excludePatterns || []
    }
  }
}