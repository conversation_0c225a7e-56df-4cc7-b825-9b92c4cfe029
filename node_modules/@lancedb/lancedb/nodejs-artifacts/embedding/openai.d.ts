import { Float } from "../arrow";
import { EmbeddingFunction } from "./embedding_function";
export type OpenAIOptions = {
    apiKey?: string;
    model?: string;
};
export declare class OpenAIEmbeddingFunction extends EmbeddingFunction<string, OpenAIOptions> {
    #private;
    constructor(options?: OpenAIOptions);
    toJSON(): {
        model: string;
    };
    ndims(): number;
    embeddingDataType(): Float;
    computeSourceEmbeddings(data: string[]): Promise<number[][]>;
    computeQueryEmbeddings(data: string): Promise<number[]>;
}
