{"include": ["lancedb/*.ts", "lancedb/**/*.ts", "lancedb/*.js"], "compilerOptions": {"target": "es2022", "module": "commonjs", "declaration": true, "outDir": "./dist", "strict": true, "allowJs": true, "resolveJsonModule": true, "emitDecoratorMetadata": true, "experimentalDecorators": true}, "exclude": ["./dist/*"], "typedocOptions": {"entryPoints": ["lancedb/index.ts"], "out": "../docs/src/javascript/", "visibilityFilters": {"protected": false, "private": false, "inherited": true, "external": false}}}