{"version": 3, "sources": ["fb/footer.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,WAAW,MAAM,aAAa,CAAC;AAE3C,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAGrC;;;;GAIG;AACH,qBAAa,MAAM;IACjB,EAAE,EAAE,WAAW,CAAC,UAAU,GAAC,IAAI,CAAQ;IACvC,MAAM,SAAK;IACX,MAAM,CAAC,CAAC,EAAC,MAAM,EAAE,EAAE,EAAC,WAAW,CAAC,UAAU,GAAE,MAAM;IAMpD,MAAM,CAAC,eAAe,CAAC,EAAE,EAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAAC,MAAM,GAAE,MAAM;IAIrE,MAAM,CAAC,2BAA2B,CAAC,EAAE,EAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,EAAC,MAAM,GAAE,MAAM;IAKjF,OAAO,IAAG,eAAe;IAKzB,MAAM,CAAC,GAAG,CAAC,EAAC,MAAM,GAAE,MAAM,GAAC,IAAI;IAK/B,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,EAAC,KAAK,GAAE,KAAK,GAAC,IAAI;IAKlD,kBAAkB,IAAG,MAAM;IAK3B,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,EAAC,KAAK,GAAE,KAAK,GAAC,IAAI;IAKnD,mBAAmB,IAAG,MAAM;IAK5B;;OAEG;IACH,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,EAAC,QAAQ,GAAE,QAAQ,GAAC,IAAI;IAK1D,oBAAoB,IAAG,MAAM;IAK7B,MAAM,CAAC,WAAW,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO;IAI9C,MAAM,CAAC,UAAU,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAC,eAAe;IAItE,MAAM,CAAC,SAAS,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,YAAY,EAAC,WAAW,CAAC,MAAM;IAI7E,MAAM,CAAC,eAAe,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,kBAAkB,EAAC,WAAW,CAAC,MAAM;IAIzF,MAAM,CAAC,uBAAuB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAC,MAAM;IAI3E,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,mBAAmB,EAAC,WAAW,CAAC,MAAM;IAI3F,MAAM,CAAC,wBAAwB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAC,MAAM;IAI5E,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,oBAAoB,EAAC,WAAW,CAAC,MAAM;IAI7F,MAAM,CAAC,0BAA0B,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,IAAI,EAAC,WAAW,CAAC,MAAM,EAAE,GAAE,WAAW,CAAC,MAAM;IAQ5G,MAAM,CAAC,yBAAyB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAC,MAAM;IAI7E,MAAM,CAAC,SAAS,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,GAAE,WAAW,CAAC,MAAM;IAKhE,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAC,WAAW,CAAC,MAAM;IAIhF,MAAM,CAAC,8BAA8B,CAAC,OAAO,EAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAC,WAAW,CAAC,MAAM;CAI3F", "file": "footer.d.ts", "sourceRoot": "../src"}