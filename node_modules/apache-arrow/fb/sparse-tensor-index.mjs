// automatically generated by the FlatBuffers compiler, do not modify
import { SparseMatrixIndexCSX } from './sparse-matrix-index-csx.mjs';
import { SparseTensorIndexCOO } from './sparse-tensor-index-coo.mjs';
import { SparseTensorIndexCSF } from './sparse-tensor-index-csf.mjs';
export var SparseTensorIndex;
(function (SparseTensorIndex) {
    SparseTensorIndex[SparseTensorIndex["NONE"] = 0] = "NONE";
    SparseTensorIndex[SparseTensorIndex["SparseTensorIndexCOO"] = 1] = "SparseTensorIndexCOO";
    SparseTensorIndex[SparseTensorIndex["SparseMatrixIndexCSX"] = 2] = "SparseMatrixIndexCSX";
    SparseTensorIndex[SparseTensorIndex["SparseTensorIndexCSF"] = 3] = "SparseTensorIndexCSF";
})(SparseTensorIndex || (SparseTensorIndex = {}));
export function unionToSparseTensorIndex(type, accessor) {
    switch (SparseTensorIndex[type]) {
        case 'NONE': return null;
        case 'SparseTensorIndexCOO': return accessor(new SparseTensorIndexCOO());
        case 'SparseMatrixIndexCSX': return accessor(new SparseMatrixIndexCSX());
        case 'SparseTensorIndexCSF': return accessor(new SparseTensorIndexCSF());
        default: return null;
    }
}
export function unionListToSparseTensorIndex(type, accessor, index) {
    switch (SparseTensorIndex[type]) {
        case 'NONE': return null;
        case 'SparseTensorIndexCOO': return accessor(index, new SparseTensorIndexCOO());
        case 'SparseMatrixIndexCSX': return accessor(index, new SparseMatrixIndexCSX());
        case 'SparseTensorIndexCSF': return accessor(index, new SparseTensorIndexCSF());
        default: return null;
    }
}

//# sourceMappingURL=sparse-tensor-index.mjs.map
