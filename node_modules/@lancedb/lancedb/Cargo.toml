[package]
name = "lancedb-nodejs"
edition.workspace = true
version = "0.0.0"
license.workspace = true
description.workspace = true
repository.workspace = true
keywords.workspace = true
categories.workspace = true

[lib]
crate-type = ["cdylib"]

[dependencies]
arrow-ipc.workspace = true
futures.workspace = true
lancedb = { path = "../rust/lancedb" }
napi = { version = "2.15", default-features = false, features = [
    "napi7",
    "async",
] }
napi-derive = "2"

# Prevent dynamic linking of lzma, which comes from datafusion
lzma-sys = { version = "*", features = ["static"] }

[build-dependencies]
napi-build = "2.1"
