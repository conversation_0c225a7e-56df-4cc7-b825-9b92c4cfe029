import * as chokidar from 'chokidar'
import { EventEmitter } from 'events'

export interface FileWatcherEvent {
  type: 'add' | 'change' | 'unlink' | 'addDir' | 'unlinkDir' | 'error'
  path: string
  stats?: any
  error?: Error
}

export interface FileWatcherOptions {
  ignored?: string | RegExp | Array<string | RegExp>
  ignoreInitial?: boolean
  followSymlinks?: boolean
  cwd?: string
  disableGlobbing?: boolean
  usePolling?: boolean
  interval?: number
  binaryInterval?: number
  alwaysStat?: boolean
  depth?: number
  awaitWriteFinish?: boolean | {
    stabilityThreshold?: number
    pollInterval?: number
  }
  ignorePermissionErrors?: boolean
  atomic?: boolean | number
}

export class FileWatcherService extends EventEmitter {
  private watchers: Map<string, chokidar.FSWatcher> = new Map()
  private isWatching = false

  constructor(private options: FileWatcherOptions = {}) {
    super()
  }

  async watch(paths: string | string[], options?: FileWatcherOptions): Promise<void> {
    if (this.isWatching) {
      throw new Error('FileWatcher is already watching')
    }

    const watchOptions = { ...this.options, ...options }
    const pathsArray = Array.isArray(paths) ? paths : [paths]

    for (const path of pathsArray) {
      const watcher = chokidar.watch(path, watchOptions)

      watcher
        .on('add', (filePath: string, stats: any) => {
          this.emit('fileEvent', {
            type: 'add',
            path: filePath,
            stats
          } as FileWatcherEvent)
        })
        .on('change', (filePath: string, stats: any) => {
          this.emit('fileEvent', {
            type: 'change',
            path: filePath,
            stats
          } as FileWatcherEvent)
        })
        .on('unlink', (filePath: string) => {
          this.emit('fileEvent', {
            type: 'unlink',
            path: filePath
          } as FileWatcherEvent)
        })
        .on('addDir', (dirPath: string, stats: any) => {
          this.emit('fileEvent', {
            type: 'addDir',
            path: dirPath,
            stats
          } as FileWatcherEvent)
        })
        .on('unlinkDir', (dirPath: string) => {
          this.emit('fileEvent', {
            type: 'unlinkDir',
            path: dirPath
          } as FileWatcherEvent)
        })
        .on('error', (error: Error) => {
          this.emit('fileEvent', {
            type: 'error',
            path: '',
            error
          } as FileWatcherEvent)
        })

      this.watchers.set(path, watcher)
    }

    this.isWatching = true
  }

  async stopWatching(path?: string): Promise<void> {
    if (path && this.watchers.has(path)) {
      const watcher = this.watchers.get(path)
      if (watcher) {
        await watcher.close()
        this.watchers.delete(path)
      }
    } else {
      for (const [watchPath, watcher] of this.watchers) {
        await watcher.close()
        this.watchers.delete(watchPath)
      }
      this.isWatching = false
    }
  }

  async stopAll(): Promise<void> {
    await this.stopWatching()
  }

  getWatchedPaths(): string[] {
    return Array.from(this.watchers.keys())
  }

  isWatchingPath(path: string): boolean {
    return this.watchers.has(path)
  }

  isActive(): boolean {
    return this.isWatching && this.watchers.size > 0
  }

  async addPath(path: string): Promise<void> {
    if (!this.isWatching) {
      throw new Error('FileWatcher is not active')
    }

    if (this.watchers.has(path)) {
      return
    }

    const watcher = chokidar.watch(path, this.options)
    this.setupWatcherEvents(watcher)
    this.watchers.set(path, watcher)
  }

  async removePath(path: string): Promise<void> {
    if (this.watchers.has(path)) {
      const watcher = this.watchers.get(path)
      if (watcher) {
        await watcher.close()
        this.watchers.delete(path)
      }
    }
  }

  private setupWatcherEvents(watcher: chokidar.FSWatcher): void {
    watcher
      .on('add', (filePath: string, stats: any) => {
        this.emit('fileEvent', {
          type: 'add',
          path: filePath,
          stats
        } as FileWatcherEvent)
      })
      .on('change', (filePath: string, stats: any) => {
        this.emit('fileEvent', {
          type: 'change',
          path: filePath,
          stats
        } as FileWatcherEvent)
      })
      .on('unlink', (filePath: string) => {
        this.emit('fileEvent', {
          type: 'unlink',
          path: filePath
        } as FileWatcherEvent)
      })
      .on('addDir', (dirPath: string, stats: any) => {
        this.emit('fileEvent', {
          type: 'addDir',
          path: dirPath,
          stats
        } as FileWatcherEvent)
      })
      .on('unlinkDir', (dirPath: string) => {
        this.emit('fileEvent', {
          type: 'unlinkDir',
          path: dirPath
        } as FileWatcherEvent)
      })
      .on('error', (error: Error) => {
        this.emit('fileEvent', {
          type: 'error',
          path: '',
          error
        } as FileWatcherEvent)
      })
  }
}