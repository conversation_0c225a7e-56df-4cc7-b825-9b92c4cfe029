// automatically generated by the FlatBuffers compiler, do not modify
import { Binary } from './binary.mjs';
import { Bool } from './bool.mjs';
import { Date } from './date.mjs';
import { Decimal } from './decimal.mjs';
import { Duration } from './duration.mjs';
import { FixedSizeBinary } from './fixed-size-binary.mjs';
import { FixedSizeList } from './fixed-size-list.mjs';
import { FloatingPoint } from './floating-point.mjs';
import { Int } from './int.mjs';
import { Interval } from './interval.mjs';
import { LargeBinary } from './large-binary.mjs';
import { LargeList } from './large-list.mjs';
import { LargeUtf8 } from './large-utf8.mjs';
import { List } from './list.mjs';
import { Map } from './map.mjs';
import { Null } from './null.mjs';
import { RunEndEncoded } from './run-end-encoded.mjs';
import { Struct_ } from './struct-.mjs';
import { Time } from './time.mjs';
import { Timestamp } from './timestamp.mjs';
import { Union } from './union.mjs';
import { Utf8 } from './utf8.mjs';
/**
 * ----------------------------------------------------------------------
 * Top-level Type value, enabling extensible type-specific metadata. We can
 * add new logical types to Type without breaking backwards compatibility
 */
export var Type;
(function (Type) {
    Type[Type["NONE"] = 0] = "NONE";
    Type[Type["Null"] = 1] = "Null";
    Type[Type["Int"] = 2] = "Int";
    Type[Type["FloatingPoint"] = 3] = "FloatingPoint";
    Type[Type["Binary"] = 4] = "Binary";
    Type[Type["Utf8"] = 5] = "Utf8";
    Type[Type["Bool"] = 6] = "Bool";
    Type[Type["Decimal"] = 7] = "Decimal";
    Type[Type["Date"] = 8] = "Date";
    Type[Type["Time"] = 9] = "Time";
    Type[Type["Timestamp"] = 10] = "Timestamp";
    Type[Type["Interval"] = 11] = "Interval";
    Type[Type["List"] = 12] = "List";
    Type[Type["Struct_"] = 13] = "Struct_";
    Type[Type["Union"] = 14] = "Union";
    Type[Type["FixedSizeBinary"] = 15] = "FixedSizeBinary";
    Type[Type["FixedSizeList"] = 16] = "FixedSizeList";
    Type[Type["Map"] = 17] = "Map";
    Type[Type["Duration"] = 18] = "Duration";
    Type[Type["LargeBinary"] = 19] = "LargeBinary";
    Type[Type["LargeUtf8"] = 20] = "LargeUtf8";
    Type[Type["LargeList"] = 21] = "LargeList";
    Type[Type["RunEndEncoded"] = 22] = "RunEndEncoded";
})(Type || (Type = {}));
export function unionToType(type, accessor) {
    switch (Type[type]) {
        case 'NONE': return null;
        case 'Null': return accessor(new Null());
        case 'Int': return accessor(new Int());
        case 'FloatingPoint': return accessor(new FloatingPoint());
        case 'Binary': return accessor(new Binary());
        case 'Utf8': return accessor(new Utf8());
        case 'Bool': return accessor(new Bool());
        case 'Decimal': return accessor(new Decimal());
        case 'Date': return accessor(new Date());
        case 'Time': return accessor(new Time());
        case 'Timestamp': return accessor(new Timestamp());
        case 'Interval': return accessor(new Interval());
        case 'List': return accessor(new List());
        case 'Struct_': return accessor(new Struct_());
        case 'Union': return accessor(new Union());
        case 'FixedSizeBinary': return accessor(new FixedSizeBinary());
        case 'FixedSizeList': return accessor(new FixedSizeList());
        case 'Map': return accessor(new Map());
        case 'Duration': return accessor(new Duration());
        case 'LargeBinary': return accessor(new LargeBinary());
        case 'LargeUtf8': return accessor(new LargeUtf8());
        case 'LargeList': return accessor(new LargeList());
        case 'RunEndEncoded': return accessor(new RunEndEncoded());
        default: return null;
    }
}
export function unionListToType(type, accessor, index) {
    switch (Type[type]) {
        case 'NONE': return null;
        case 'Null': return accessor(index, new Null());
        case 'Int': return accessor(index, new Int());
        case 'FloatingPoint': return accessor(index, new FloatingPoint());
        case 'Binary': return accessor(index, new Binary());
        case 'Utf8': return accessor(index, new Utf8());
        case 'Bool': return accessor(index, new Bool());
        case 'Decimal': return accessor(index, new Decimal());
        case 'Date': return accessor(index, new Date());
        case 'Time': return accessor(index, new Time());
        case 'Timestamp': return accessor(index, new Timestamp());
        case 'Interval': return accessor(index, new Interval());
        case 'List': return accessor(index, new List());
        case 'Struct_': return accessor(index, new Struct_());
        case 'Union': return accessor(index, new Union());
        case 'FixedSizeBinary': return accessor(index, new FixedSizeBinary());
        case 'FixedSizeList': return accessor(index, new FixedSizeList());
        case 'Map': return accessor(index, new Map());
        case 'Duration': return accessor(index, new Duration());
        case 'LargeBinary': return accessor(index, new LargeBinary());
        case 'LargeUtf8': return accessor(index, new LargeUtf8());
        case 'LargeList': return accessor(index, new LargeList());
        case 'RunEndEncoded': return accessor(index, new RunEndEncoded());
        default: return null;
    }
}

//# sourceMappingURL=type.mjs.map
