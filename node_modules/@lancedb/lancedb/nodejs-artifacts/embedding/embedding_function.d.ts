import "reflect-metadata";
import { DataType, Float, type IntoVector } from "../arrow";
/**
 * Options for a given embedding function
 */
export interface FunctionOptions {
    [key: string]: any;
}
/**
 * An embedding function that automatically creates vector representation for a given column.
 */
export declare abstract class EmbeddingFunction<T = any, M extends FunctionOptions = FunctionOptions> {
    /**
     * Convert the embedding function to a JSON object
     * It is used to serialize the embedding function to the schema
     * It's important that any object returned by this method contains all the necessary
     * information to recreate the embedding function
     *
     * It should return the same object that was passed to the constructor
     * If it does not, the embedding function will not be able to be recreated, or could be recreated incorrectly
     *
     * @example
     * ```ts
     * class MyEmbeddingFunction extends EmbeddingFunction {
     *   constructor(options: {model: string, timeout: number}) {
     *     super();
     *     this.model = options.model;
     *     this.timeout = options.timeout;
     *   }
     *   toJSON() {
     *     return {
     *       model: this.model,
     *       timeout: this.timeout,
     *     };
     * }
     * ```
     */
    abstract toJSON(): Partial<M>;
    /**
     * sourceField is used in combination with `LanceSchema` to provide a declarative data model
     *
     * @param optionsOrDatatype - The options for the field or the datatype
     *
     * @see {@link lancedb.LanceSchema}
     */
    sourceField(optionsOrDatatype: Partial<FieldOptions> | DataType): [DataType, Map<string, EmbeddingFunction>];
    /**
     * vectorField is used in combination with `LanceSchema` to provide a declarative data model
     *
     * @param options - The options for the field
     *
     * @see {@link lancedb.LanceSchema}
     */
    vectorField(optionsOrDatatype?: Partial<FieldOptions> | DataType): [DataType, Map<string, EmbeddingFunction>];
    /** The number of dimensions of the embeddings */
    ndims(): number | undefined;
    /** The datatype of the embeddings */
    abstract embeddingDataType(): Float;
    /**
     * Creates a vector representation for the given values.
     */
    abstract computeSourceEmbeddings(data: T[]): Promise<number[][] | Float32Array[] | Float64Array[]>;
    /**
    Compute the embeddings for a single query
   */
    computeQueryEmbeddings(data: T): Promise<IntoVector>;
}
export interface FieldOptions<T extends DataType = DataType> {
    datatype: T;
    dims?: number;
}
