// Type definitions for antlr4 4.7
// Project: https://github.com/antlr/antlr4
// Definitions by: <PERSON><PERSON> <https://github.com/mcchatman8009>
//                 <PERSON> <https://github.com/tarilabs>
//                 <PERSON> <https://github.com/jgellin-sf>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped
export * from './Lexer';
export * from './Parser';
export * from './Recognizer';
export * from './ParserRuleContext';
export * from './IntervalSet';
export * from './CommonTokenStream';
export * from './InputStream';
export * from './Token';
import { ParserRuleContext } from './ParserRuleContext';

export type AntlrRule = ParserRuleContext;
