{"version": 3, "sources": ["fb/sparse-tensor.ts"], "names": [], "mappings": ";AAAA,qEAAqE;;;AAErE,2CAA2C;AAE3C,2CAAqC;AACrC,qEAA6D;AAC7D,mDAA4C;AAC5C,uCAAiC;AAGjC,MAAa,YAAY;IAAzB;QACE,OAAE,GAAgC,IAAI,CAAC;QACvC,WAAM,GAAG,CAAC,CAAC;IAsIb,CAAC;IArIC,MAAM,CAAC,CAAQ,EAAE,EAAyB;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,EAAyB,EAAE,GAAiB;QACvE,OAAO,CAAC,GAAG,IAAI,IAAI,YAAY,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7F,CAAC;IAED,MAAM,CAAC,iCAAiC,CAAC,EAAyB,EAAE,GAAiB;QACnF,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,YAAY,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7F,CAAC;IAED,QAAQ;QACN,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC;IACvE,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,GAAO;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAa,EAAE,GAAc;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,yBAAS,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7I,CAAC;IAED,WAAW;QACT,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACH,aAAa;QACX,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACzE,CAAC;IAED,eAAe;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,0CAAiB,CAAC,IAAI,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,GAAO;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,GAAW;QACd,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,kBAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,IAAI,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACtF,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,OAA2B;QAClD,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAA2B,EAAE,QAAa;QAC3D,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,QAAQ,EAAE,cAAI,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,OAA2B,EAAE,UAA6B;QACvE,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,OAA2B,EAAE,WAA8B;QACzE,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,OAA2B,EAAE,IAAyB;QAC7E,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACzC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAE,CAAC,CAAC;SAC7B;QACD,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,OAA2B,EAAE,QAAe;QAClE,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,OAA2B,EAAE,aAAoB;QACvE,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,OAA2B,EAAE,eAAiC;QACtF,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,eAAe,EAAE,0CAAiB,CAAC,IAAI,CAAC,CAAC;IACnE,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,OAA2B,EAAE,iBAAoC;QACrF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,OAA2B,EAAE,UAA6B;QACvE,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,OAA2B;QAChD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA,CAAC,OAAO;QACxC,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA,CAAC,QAAQ;QACzC,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA,CAAC,cAAc;QAChD,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA,CAAC,OAAO;QACzC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,wBAAwB,CAAC,OAA2B,EAAE,MAAyB;QACpF,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,oCAAoC,CAAC,OAA2B,EAAE,MAAyB;QAChG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;CAEA;AAxID,oCAwIC", "file": "sparse-tensor.js", "sourceRoot": "../src"}