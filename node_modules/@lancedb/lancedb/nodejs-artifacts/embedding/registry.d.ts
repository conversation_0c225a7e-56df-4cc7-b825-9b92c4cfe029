import type { EmbeddingFunction } from "./embedding_function";
import "reflect-metadata";
export interface EmbeddingFunctionOptions {
    [key: string]: unknown;
}
export interface EmbeddingFunctionFactory<T extends EmbeddingFunction = EmbeddingFunction> {
    new (modelOptions?: EmbeddingFunctionOptions): T;
}
interface EmbeddingFunctionCreate<T extends EmbeddingFunction> {
    create(options?: EmbeddingFunctionOptions): T;
}
/**
 * This is a singleton class used to register embedding functions
 * and fetch them by name. It also handles serializing and deserializing.
 * You can implement your own embedding function by subclassing EmbeddingFunction
 * or TextEmbeddingFunction and registering it with the registry
 */
export declare class EmbeddingFunctionRegistry {
    #private;
    /**
     * Register an embedding function
     * @param name The name of the function
     * @param func The function to register
     * @throws Error if the function is already registered
     */
    register<T extends EmbeddingFunctionFactory = EmbeddingFunctionFactory>(this: EmbeddingFunctionRegistry, alias?: string): (ctor: T) => any;
    /**
     * Fetch an embedding function by name
     * @param name The name of the function
     */
    get<T extends EmbeddingFunction<unknown> = EmbeddingFunction>(name: string): EmbeddingFunctionCreate<T> | undefined;
    /**
     * reset the registry to the initial state
     */
    reset(this: EmbeddingFunctionRegistry): void;
    /**
     * @ignore
     */
    parseFunctions(this: EmbeddingFunctionRegistry, metadata: Map<string, string>): Map<string, EmbeddingFunctionConfig>;
    functionToMetadata(conf: EmbeddingFunctionConfig): Record<string, any>;
    getTableMetadata(functions: EmbeddingFunctionConfig[]): Map<string, string>;
}
export declare function register(name?: string): (ctor: EmbeddingFunctionFactory<EmbeddingFunction<any, import("./embedding_function").FunctionOptions>>) => any;
/**
 * Utility function to get the global instance of the registry
 * @returns `EmbeddingFunctionRegistry` The global instance of the registry
 * @example
 * ```ts
 * const registry = getRegistry();
 * const openai = registry.get("openai").create();
 */
export declare function getRegistry(): EmbeddingFunctionRegistry;
export interface EmbeddingFunctionConfig {
    sourceColumn: string;
    vectorColumn?: string;
    function: EmbeddingFunction;
}
export {};
