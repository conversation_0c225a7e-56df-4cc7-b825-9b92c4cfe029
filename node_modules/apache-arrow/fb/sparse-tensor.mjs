// automatically generated by the FlatBuffers compiler, do not modify
import * as flatbuffers from 'flatbuffers';
import { <PERSON><PERSON><PERSON> } from './buffer.mjs';
import { SparseTensorIndex } from './sparse-tensor-index.mjs';
import { TensorDim } from './tensor-dim.mjs';
import { Type } from './type.mjs';
export class SparseTensor {
    constructor() {
        this.bb = null;
        this.bb_pos = 0;
    }
    __init(i, bb) {
        this.bb_pos = i;
        this.bb = bb;
        return this;
    }
    static getRootAsSparseTensor(bb, obj) {
        return (obj || new SparseTensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    static getSizePrefixedRootAsSparseTensor(bb, obj) {
        bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
        return (obj || new SparseTensor()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    typeType() {
        const offset = this.bb.__offset(this.bb_pos, 4);
        return offset ? this.bb.readUint8(this.bb_pos + offset) : Type.NONE;
    }
    /**
     * The type of data contained in a value cell.
     * Currently only fixed-width value types are supported,
     * no strings or nested types.
     */
    type(obj) {
        const offset = this.bb.__offset(this.bb_pos, 6);
        return offset ? this.bb.__union(obj, this.bb_pos + offset) : null;
    }
    /**
     * The dimensions of the tensor, optionally named.
     */
    shape(index, obj) {
        const offset = this.bb.__offset(this.bb_pos, 8);
        return offset ? (obj || new TensorDim()).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos + offset) + index * 4), this.bb) : null;
    }
    shapeLength() {
        const offset = this.bb.__offset(this.bb_pos, 8);
        return offset ? this.bb.__vector_len(this.bb_pos + offset) : 0;
    }
    /**
     * The number of non-zero values in a sparse tensor.
     */
    nonZeroLength() {
        const offset = this.bb.__offset(this.bb_pos, 10);
        return offset ? this.bb.readInt64(this.bb_pos + offset) : BigInt('0');
    }
    sparseIndexType() {
        const offset = this.bb.__offset(this.bb_pos, 12);
        return offset ? this.bb.readUint8(this.bb_pos + offset) : SparseTensorIndex.NONE;
    }
    /**
     * Sparse tensor index
     */
    sparseIndex(obj) {
        const offset = this.bb.__offset(this.bb_pos, 14);
        return offset ? this.bb.__union(obj, this.bb_pos + offset) : null;
    }
    /**
     * The location and size of the tensor's data
     */
    data(obj) {
        const offset = this.bb.__offset(this.bb_pos, 16);
        return offset ? (obj || new Buffer()).__init(this.bb_pos + offset, this.bb) : null;
    }
    static startSparseTensor(builder) {
        builder.startObject(7);
    }
    static addTypeType(builder, typeType) {
        builder.addFieldInt8(0, typeType, Type.NONE);
    }
    static addType(builder, typeOffset) {
        builder.addFieldOffset(1, typeOffset, 0);
    }
    static addShape(builder, shapeOffset) {
        builder.addFieldOffset(2, shapeOffset, 0);
    }
    static createShapeVector(builder, data) {
        builder.startVector(4, data.length, 4);
        for (let i = data.length - 1; i >= 0; i--) {
            builder.addOffset(data[i]);
        }
        return builder.endVector();
    }
    static startShapeVector(builder, numElems) {
        builder.startVector(4, numElems, 4);
    }
    static addNonZeroLength(builder, nonZeroLength) {
        builder.addFieldInt64(3, nonZeroLength, BigInt('0'));
    }
    static addSparseIndexType(builder, sparseIndexType) {
        builder.addFieldInt8(4, sparseIndexType, SparseTensorIndex.NONE);
    }
    static addSparseIndex(builder, sparseIndexOffset) {
        builder.addFieldOffset(5, sparseIndexOffset, 0);
    }
    static addData(builder, dataOffset) {
        builder.addFieldStruct(6, dataOffset, 0);
    }
    static endSparseTensor(builder) {
        const offset = builder.endObject();
        builder.requiredField(offset, 6); // type
        builder.requiredField(offset, 8); // shape
        builder.requiredField(offset, 14); // sparseIndex
        builder.requiredField(offset, 16); // data
        return offset;
    }
    static finishSparseTensorBuffer(builder, offset) {
        builder.finish(offset);
    }
    static finishSizePrefixedSparseTensorBuffer(builder, offset) {
        builder.finish(offset, undefined, true);
    }
}

//# sourceMappingURL=sparse-tensor.mjs.map
