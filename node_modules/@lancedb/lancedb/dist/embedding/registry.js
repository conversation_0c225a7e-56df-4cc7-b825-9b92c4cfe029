"use strict";
// Copyright 2024 Lance Developers.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.getRegistry = exports.register = exports.EmbeddingFunctionRegistry = void 0;
require("reflect-metadata");
/**
 * This is a singleton class used to register embedding functions
 * and fetch them by name. It also handles serializing and deserializing.
 * You can implement your own embedding function by subclassing EmbeddingFunction
 * or TextEmbeddingFunction and registering it with the registry
 */
class EmbeddingFunctionRegistry {
    #functions = new Map();
    /**
     * Register an embedding function
     * @param name The name of the function
     * @param func The function to register
     * @throws Error if the function is already registered
     */
    register(alias) {
        const self = this;
        return function (ctor) {
            if (!alias) {
                alias = ctor.name;
            }
            if (self.#functions.has(alias)) {
                throw new Error(`Embedding function with alias "${alias}" already exists`);
            }
            self.#functions.set(alias, ctor);
            Reflect.defineMetadata("lancedb::embedding::name", alias, ctor);
            return ctor;
        };
    }
    /**
     * Fetch an embedding function by name
     * @param name The name of the function
     */
    get(name) {
        const factory = this.#functions.get(name);
        if (!factory) {
            return undefined;
        }
        return {
            create: function (options) {
                return new factory(options);
            },
        };
    }
    /**
     * reset the registry to the initial state
     */
    reset() {
        this.#functions.clear();
    }
    /**
     * @ignore
     */
    parseFunctions(metadata) {
        if (!metadata.has("embedding_functions")) {
            return new Map();
        }
        else {
            const functions = (JSON.parse(metadata.get("embedding_functions")));
            return new Map(functions.map((f) => {
                const fn = this.get(f.name);
                if (!fn) {
                    throw new Error(`Function "${f.name}" not found in registry`);
                }
                return [
                    f.name,
                    {
                        sourceColumn: f.sourceColumn,
                        vectorColumn: f.vectorColumn,
                        function: this.get(f.name).create(f.model),
                    },
                ];
            }));
        }
    }
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    functionToMetadata(conf) {
        // biome-ignore lint/suspicious/noExplicitAny: <explanation>
        const metadata = {};
        const name = Reflect.getMetadata("lancedb::embedding::name", conf.function.constructor);
        metadata["sourceColumn"] = conf.sourceColumn;
        metadata["vectorColumn"] = conf.vectorColumn ?? "vector";
        metadata["name"] = name ?? conf.function.constructor.name;
        metadata["model"] = conf.function.toJSON();
        return metadata;
    }
    getTableMetadata(functions) {
        const metadata = new Map();
        const jsonData = functions.map((conf) => this.functionToMetadata(conf));
        metadata.set("embedding_functions", JSON.stringify(jsonData));
        return metadata;
    }
}
exports.EmbeddingFunctionRegistry = EmbeddingFunctionRegistry;
const _REGISTRY = new EmbeddingFunctionRegistry();
function register(name) {
    return _REGISTRY.register(name);
}
exports.register = register;
/**
 * Utility function to get the global instance of the registry
 * @returns `EmbeddingFunctionRegistry` The global instance of the registry
 * @example
 * ```ts
 * const registry = getRegistry();
 * const openai = registry.get("openai").create();
 */
function getRegistry() {
    return _REGISTRY;
}
exports.getRegistry = getRegistry;
