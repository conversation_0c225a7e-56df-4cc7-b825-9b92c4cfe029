"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.unionListToType = exports.unionToType = exports.Type = exports.TensorDim = exports.SparseTensorIndexCSF = exports.SparseTensorIndexCOO = exports.unionListToSparseTensorIndex = exports.unionToSparseTensorIndex = exports.SparseTensorIndex = exports.SparseTensor = exports.SparseMatrixIndexCSX = exports.SparseMatrixCompressedAxis = exports.Int = exports.Buffer = void 0;
var buffer_js_1 = require("./buffer.js");
Object.defineProperty(exports, "Buffer", { enumerable: true, get: function () { return buffer_js_1.Buffer; } });
var int_js_1 = require("./int.js");
Object.defineProperty(exports, "Int", { enumerable: true, get: function () { return int_js_1.Int; } });
var sparse_matrix_compressed_axis_js_1 = require("./sparse-matrix-compressed-axis.js");
Object.defineProperty(exports, "SparseMatrixCompressedAxis", { enumerable: true, get: function () { return sparse_matrix_compressed_axis_js_1.SparseMatrixCompressedAxis; } });
var sparse_matrix_index_csx_js_1 = require("./sparse-matrix-index-csx.js");
Object.defineProperty(exports, "SparseMatrixIndexCSX", { enumerable: true, get: function () { return sparse_matrix_index_csx_js_1.SparseMatrixIndexCSX; } });
var sparse_tensor_js_1 = require("./sparse-tensor.js");
Object.defineProperty(exports, "SparseTensor", { enumerable: true, get: function () { return sparse_tensor_js_1.SparseTensor; } });
var sparse_tensor_index_js_1 = require("./sparse-tensor-index.js");
Object.defineProperty(exports, "SparseTensorIndex", { enumerable: true, get: function () { return sparse_tensor_index_js_1.SparseTensorIndex; } });
Object.defineProperty(exports, "unionToSparseTensorIndex", { enumerable: true, get: function () { return sparse_tensor_index_js_1.unionToSparseTensorIndex; } });
Object.defineProperty(exports, "unionListToSparseTensorIndex", { enumerable: true, get: function () { return sparse_tensor_index_js_1.unionListToSparseTensorIndex; } });
var sparse_tensor_index_coo_js_1 = require("./sparse-tensor-index-coo.js");
Object.defineProperty(exports, "SparseTensorIndexCOO", { enumerable: true, get: function () { return sparse_tensor_index_coo_js_1.SparseTensorIndexCOO; } });
var sparse_tensor_index_csf_js_1 = require("./sparse-tensor-index-csf.js");
Object.defineProperty(exports, "SparseTensorIndexCSF", { enumerable: true, get: function () { return sparse_tensor_index_csf_js_1.SparseTensorIndexCSF; } });
var tensor_dim_js_1 = require("./tensor-dim.js");
Object.defineProperty(exports, "TensorDim", { enumerable: true, get: function () { return tensor_dim_js_1.TensorDim; } });
var type_js_1 = require("./type.js");
Object.defineProperty(exports, "Type", { enumerable: true, get: function () { return type_js_1.Type; } });
Object.defineProperty(exports, "unionToType", { enumerable: true, get: function () { return type_js_1.unionToType; } });
Object.defineProperty(exports, "unionListToType", { enumerable: true, get: function () { return type_js_1.unionListToType; } });

//# sourceMappingURL=SparseTensor_generated.js.map
