import { AIError, RetryConfig } from '../../types/aiTypes'
import { Logger } from '../../utils/logger'

export class AIErrorHandler {
  private readonly defaultRetryConfig: RetryConfig = {
    maxAttempts: 3,
    initialDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    retryableErrors: [
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      'ECONNRESET',
      'rate_limit_exceeded',
      'server_error',
      'timeout'
    ]
  }

  constructor (private readonly logger: Logger) {}

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    providerName: string,
    customConfig?: Partial<RetryConfig>
  ): Promise<T> {
    const config = { ...this.defaultRetryConfig, ...customConfig }
    let lastError: Error | null = null
    let delay = config.initialDelay

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        this.logger.debug(`${providerName}: Attempt ${attempt}/${config.maxAttempts}`)
        const result = await operation()

        if (attempt > 1) {
          this.logger.info(`${providerName}: Operation succeeded on attempt ${attempt}`)
        }

        return result
      } catch (error) {
        lastError = error as Error

        this.logger.warn(`${providerName}: Attempt ${attempt} failed:`, error)

        if (attempt === config.maxAttempts) {
          this.logger.error(`${providerName}: All retry attempts exhausted`)
          break
        }

        if (!this.isRetryableError(error, config.retryableErrors)) {
          this.logger.error(`${providerName}: Non-retryable error, aborting:`, error)
          break
        }

        this.logger.info(`${providerName}: Retrying in ${delay}ms...`)
        await this.sleep(delay)
        delay = Math.min(delay * config.backoffFactor, config.maxDelay)
      }
    }

    throw lastError || new Error('Operation failed after all retry attempts')
  }

  private isRetryableError (error: any, retryableErrors: string[]): boolean {
    // Check error code
    if (error.code && retryableErrors.includes(error.code)) {
      return true
    }

    // Check error message for known patterns
    const errorMessage = error.message?.toLowerCase() || ''
    for (const retryableError of retryableErrors) {
      if (errorMessage.includes(retryableError.toLowerCase())) {
        return true
      }
    }

    // Check HTTP status codes
    if (error.response?.status) {
      const status = error.response.status
      // Retry on server errors and rate limits
      if (status >= 500 || status === 429) {
        return true
      }
    }

    // Check for specific API errors
    if (error.type) {
      switch (error.type) {
        case 'server_error':
        case 'timeout':
        case 'rate_limit_exceeded':
          return true
        default:
          return false
      }
    }

    return false
  }

  private async sleep (ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  createAIError (
    code: string,
    message: string,
    provider: string,
    retryable: boolean = false,
    details?: any
  ): AIError {
    return {
      code,
      message,
      provider,
      retryable,
      details
    }
  }

  handleOpenAIError (error: any): AIError {
    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      switch (status) {
        case 400:
          return this.createAIError(
            'INVALID_REQUEST',
            `Invalid request: ${data?.error?.message || 'Bad request'}`,
            'openai',
            false,
            { status, data }
          )
        case 401:
          return this.createAIError(
            'AUTHENTICATION_ERROR',
            'Invalid or missing API key',
            'openai',
            false,
            { status, data }
          )
        case 403:
          return this.createAIError(
            'PERMISSION_ERROR',
            'Access denied or insufficient permissions',
            'openai',
            false,
            { status, data }
          )
        case 404:
          return this.createAIError(
            'NOT_FOUND',
            'Model or resource not found',
            'openai',
            false,
            { status, data }
          )
        case 429:
          return this.createAIError(
            'RATE_LIMIT_EXCEEDED',
            'Rate limit exceeded, please try again later',
            'openai',
            true,
            { status, data }
          )
        case 500:
        case 502:
        case 503:
        case 504:
          return this.createAIError(
            'SERVER_ERROR',
            `OpenAI server error: ${status}`,
            'openai',
            true,
            { status, data }
          )
        default:
          return this.createAIError(
            'UNKNOWN_ERROR',
            `OpenAI API error: ${data?.error?.message || 'Unknown error'}`,
            'openai',
            false,
            { status, data }
          )
      }
    }

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return this.createAIError(
        'CONNECTION_ERROR',
        'Cannot connect to OpenAI API',
        'openai',
        true,
        { code: error.code }
      )
    }

    if (error.code === 'ETIMEDOUT') {
      return this.createAIError(
        'TIMEOUT_ERROR',
        'Request timed out',
        'openai',
        true,
        { code: error.code }
      )
    }

    return this.createAIError(
      'UNKNOWN_ERROR',
      `OpenAI provider error: ${error.message}`,
      'openai',
      false,
      { originalError: error }
    )
  }

  handleClaudeError (error: any): AIError {
    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      switch (status) {
        case 400:
          return this.createAIError(
            'INVALID_REQUEST',
            `Invalid request: ${data?.error?.message || 'Bad request'}`,
            'claude',
            false,
            { status, data }
          )
        case 401:
          return this.createAIError(
            'AUTHENTICATION_ERROR',
            'Invalid or missing API key',
            'claude',
            false,
            { status, data }
          )
        case 403:
          return this.createAIError(
            'PERMISSION_ERROR',
            'Access denied or insufficient permissions',
            'claude',
            false,
            { status, data }
          )
        case 404:
          return this.createAIError(
            'NOT_FOUND',
            'Model or resource not found',
            'claude',
            false,
            { status, data }
          )
        case 429:
          return this.createAIError(
            'RATE_LIMIT_EXCEEDED',
            'Rate limit exceeded, please try again later',
            'claude',
            true,
            { status, data }
          )
        case 500:
        case 502:
        case 503:
        case 504:
          return this.createAIError(
            'SERVER_ERROR',
            `Claude server error: ${status}`,
            'claude',
            true,
            { status, data }
          )
        default:
          return this.createAIError(
            'UNKNOWN_ERROR',
            `Claude API error: ${data?.error?.message || 'Unknown error'}`,
            'claude',
            false,
            { status, data }
          )
      }
    }

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return this.createAIError(
        'CONNECTION_ERROR',
        'Cannot connect to Claude API',
        'claude',
        true,
        { code: error.code }
      )
    }

    if (error.code === 'ETIMEDOUT') {
      return this.createAIError(
        'TIMEOUT_ERROR',
        'Request timed out',
        'claude',
        true,
        { code: error.code }
      )
    }

    return this.createAIError(
      'UNKNOWN_ERROR',
      `Claude provider error: ${error.message}`,
      'claude',
      false,
      { originalError: error }
    )
  }

  formatErrorForUser (error: AIError): string {
    switch (error.code) {
      case 'AUTHENTICATION_ERROR':
        return 'Please check your API key configuration in settings.'
      case 'RATE_LIMIT_EXCEEDED':
        return 'Rate limit exceeded. Please wait a moment and try again.'
      case 'CONNECTION_ERROR':
        return 'Unable to connect to the AI service. Please check your internet connection.'
      case 'TIMEOUT_ERROR':
        return 'Request timed out. Please try again.'
      case 'INVALID_REQUEST':
        return 'Invalid request format. Please check your input.'
      case 'PERMISSION_ERROR':
        return 'Access denied. Please check your API key permissions.'
      case 'NOT_FOUND':
        return 'The requested model or resource was not found.'
      case 'SERVER_ERROR':
        return 'The AI service is temporarily unavailable. Please try again later.'
      default:
        return error.message || 'An unknown error occurred.'
    }
  }

  shouldShowRetryOption (error: AIError): boolean {
    return error.retryable
  }

  getRetryDelay (error: AIError): number {
    switch (error.code) {
      case 'RATE_LIMIT_EXCEEDED':
        return 60000 // 1 minute
      case 'SERVER_ERROR':
        return 30000 // 30 seconds
      case 'CONNECTION_ERROR':
      case 'TIMEOUT_ERROR':
        return 5000 // 5 seconds
      default:
        return 0
    }
  }
}
