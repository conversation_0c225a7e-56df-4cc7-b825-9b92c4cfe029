{"version": 3, "sources": ["fb/dictionary-encoding.ts"], "names": [], "mappings": ";AAAA,qEAAqE;;;AAErE,2CAA2C;AAE3C,6DAAsD;AACtD,qCAA+B;AAG/B,MAAa,kBAAkB;IAA/B;QACE,OAAE,GAAgC,IAAI,CAAC;QACvC,WAAM,GAAG,CAAC,CAAC;IA+Eb,CAAC;IA9EC,MAAM,CAAC,CAAQ,EAAE,EAAyB;QAC1C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,2BAA2B,CAAC,EAAyB,EAAE,GAAuB;QACnF,OAAO,CAAC,GAAG,IAAI,IAAI,kBAAkB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACnG,CAAC;IAED,MAAM,CAAC,uCAAuC,CAAC,EAAyB,EAAE,GAAuB;QAC/F,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,IAAI,IAAI,kBAAkB,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACnG,CAAC;IAED;;;;OAIG;IACH,EAAE;QACA,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACzE,CAAC;IAED;;;;;;OAMG;IACH,SAAS,CAAC,GAAQ;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,YAAG,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,EAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACxG,CAAC;IAED;;;;;OAKG;IACH,SAAS;QACP,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACpE,CAAC;IAED,cAAc;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,EAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAG,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,mCAAc,CAAC,UAAU,CAAC;IACvF,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,OAA2B;QACxD,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,OAA2B,EAAE,EAAS;QACjD,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAA2B,EAAE,eAAkC;QACjF,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAA2B,EAAE,SAAiB;QAChE,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,MAAM,CAAC,iBAAiB,CAAC,OAA2B,EAAE,cAA6B;QACjF,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,cAAc,EAAE,mCAAc,CAAC,UAAU,CAAC,CAAC;IACtE,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,OAA2B;QACtD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC;IAChB,CAAC;CAEA;AAjFD,gDAiFC", "file": "dictionary-encoding.js", "sourceRoot": "../src"}