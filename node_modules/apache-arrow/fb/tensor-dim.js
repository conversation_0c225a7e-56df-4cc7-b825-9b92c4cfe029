"use strict";
// automatically generated by the FlatBuffers compiler, do not modify
Object.defineProperty(exports, "__esModule", { value: true });
exports.TensorDim = void 0;
const flatbuffers = require("flatbuffers");
/**
 * ----------------------------------------------------------------------
 * Data structures for dense tensors
 * Shape data for a single axis in a tensor
 */
class TensorDim {
    constructor() {
        this.bb = null;
        this.bb_pos = 0;
    }
    __init(i, bb) {
        this.bb_pos = i;
        this.bb = bb;
        return this;
    }
    static getRootAsTensorDim(bb, obj) {
        return (obj || new TensorDim()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    static getSizePrefixedRootAsTensorDim(bb, obj) {
        bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
        return (obj || new TensorDim()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
    }
    /**
     * Length of dimension
     */
    size() {
        const offset = this.bb.__offset(this.bb_pos, 4);
        return offset ? this.bb.readInt64(this.bb_pos + offset) : BigInt('0');
    }
    name(optionalEncoding) {
        const offset = this.bb.__offset(this.bb_pos, 6);
        return offset ? this.bb.__string(this.bb_pos + offset, optionalEncoding) : null;
    }
    static startTensorDim(builder) {
        builder.startObject(2);
    }
    static addSize(builder, size) {
        builder.addFieldInt64(0, size, BigInt('0'));
    }
    static addName(builder, nameOffset) {
        builder.addFieldOffset(1, nameOffset, 0);
    }
    static endTensorDim(builder) {
        const offset = builder.endObject();
        return offset;
    }
    static createTensorDim(builder, size, nameOffset) {
        TensorDim.startTensorDim(builder);
        TensorDim.addSize(builder, size);
        TensorDim.addName(builder, nameOffset);
        return TensorDim.endTensorDim(builder);
    }
}
exports.TensorDim = TensorDim;

//# sourceMappingURL=tensor-dim.js.map
