{"version": 3, "sources": ["builder/fixedsizebinary.ts"], "names": [], "mappings": "AAAA,6DAA6D;AAC7D,+DAA+D;AAC/D,wDAAwD;AACxD,6DAA6D;AAC7D,oDAAoD;AACpD,6DAA6D;AAC7D,6DAA6D;AAC7D,EAAE;AACF,+CAA+C;AAC/C,EAAE;AACF,6DAA6D;AAC7D,8DAA8D;AAC9D,yDAAyD;AACzD,4DAA4D;AAC5D,0DAA0D;AAC1D,qBAAqB;AAGrB,OAAO,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAClD,OAAO,EAAE,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAEvD,cAAc;AACd,MAAM,OAAO,sBAAoC,SAAQ,iBAAyC;CAAI;AAErG,sBAAsB,CAAC,SAAiB,CAAC,SAAS,GAAG,kBAAkB,CAAC", "file": "fixedsizebinary.js", "sourceRoot": "../src"}